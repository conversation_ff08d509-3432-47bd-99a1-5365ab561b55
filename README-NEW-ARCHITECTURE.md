# LiveStream - 现代化 PHP 直播录制扩展包

一个现代化、模块化的 PHP 直播录制扩展包，专注于提供清晰的 API 和强类型支持。

## 🎯 设计理念

这个扩展包遵循以下设计原则：

- **关注点分离**: 扩展包专注于核心录制逻辑，进程管理交给客户端
- **强类型**: 使用 PHP 8.1+ 的现代特性，提供完整的类型安全
- **模块化**: 清晰的接口定义，易于扩展和测试
- **配置对象**: 使用强类型配置类替代数组配置
- **不可变性**: 配置对象是只读的，确保线程安全

## 🚀 快速开始

### 安装

```bash
composer require your-vendor/live-stream
```

### 基础用法

```php
<?php
use LiveStream\LiveStreamManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

// 创建管理器
$manager = LiveStreamManager::create();

// 创建录制配置
$options = new RecordingOptions(
    quality: Quality::ORIGINAL,
    format: OutputFormat::MP4,
    savePath: './recordings',
    createFolderByPlatform: true,
);

// 执行录制
$result = $manager->record('https://live.douyin.com/123456', $options);

if ($result->isSuccessful()) {
    $command = $result->getCommand();
    
    // 客户端选择执行方式
    use Symfony\Component\Process\Process;
    $process = new Process($command);
    $process->start();
    
    echo "录制已启动: {$result->getOutputPath()}\n";
} else {
    echo "录制失败: {$result->getError()}\n";
}
```

## 📚 核心概念

### 1. 配置对象化

使用强类型配置类替代数组：

```php
// ✅ 新方式：强类型配置
$options = new RecordingOptions(
    quality: Quality::ORIGINAL,
    format: OutputFormat::MP4,
    savePath: './recordings',
    splitTime: 3600, // 1小时分段
);

// ❌ 旧方式：数组配置
$options = [
    'quality' => '原画',
    'format' => 'mp4',
    'save_path' => './recordings',
    'split_time' => 3600,
];
```

### 2. 接口依赖反转

录制器依赖房间信息对象而非 URL：

```php
// ✅ 新方式：依赖抽象
interface RecorderInterface {
    public function record(RoomInfoInterface $roomInfo, RecordingOptions $options): RecordingResultInterface;
}

// ❌ 旧方式：依赖具体实现
interface RecorderInterface {
    public function startRecord(string $url, array $options = []): bool;
}
```

### 3. 结果对象

录制操作返回结构化的结果对象：

```php
$result = $recorder->record($roomInfo, $options);

// 检查结果
if ($result->isSuccessful()) {
    $command = $result->getCommand();      // FFmpeg 命令
    $outputPath = $result->getOutputPath(); // 输出文件路径
    $metadata = $result->getMetadata();    // 录制元数据
} else {
    $error = $result->getError();          // 错误信息
}
```

### 4. 进程管理解耦

扩展包只负责构建录制命令，客户端选择执行方式：

```php
// 选项1：使用 Symfony Process
use Symfony\Component\Process\Process;
$process = new Process($result->getCommand());
$process->start();

// 选项2：使用 Swoole
use Swoole\Process;
$pid = Process::exec('ffmpeg', array_slice($command, 1));

// 选项3：自定义进程管理
$customProcessManager->execute($result->getCommand());
```

## 🏗️ 架构设计

### 目录结构

```
src/
├── Config/                 # 配置类
│   ├── RecordingOptions.php
│   └── StreamConfig.php
├── Contracts/              # 接口定义
│   ├── PlatformInterface.php
│   ├── RecorderInterface.php
│   ├── RecordingResultInterface.php
│   └── RoomInfoInterface.php
├── Enum/                   # 枚举类型
│   ├── Quality.php
│   └── OutputFormat.php
├── Factory/                # 工厂类
│   └── RecorderFactory.php
├── Platforms/              # 平台实现
│   └── Douyin/
├── Recording/              # 录制实现
│   ├── StreamRecorder.php
│   ├── FFmpegCommand.php
│   └── RecordingResult.php
├── Utils/                  # 工具类
│   └── PathBuilder.php
├── Validation/             # 验证层
│   └── ConfigValidator.php
├── LiveStream.php          # 平台驱动管理器
├── LiveStreamManager.php   # 高级 API 管理器
└── PlatformFactory.php     # 平台工厂
```

### 核心组件

#### 1. LiveStreamManager
高级 API，提供最简单的使用方式：

```php
$manager = LiveStreamManager::create();
$result = $manager->record($url, $options);
```

#### 2. RecorderInterface
录制器接口，定义核心录制功能：

```php
interface RecorderInterface {
    public function record(RoomInfoInterface $roomInfo, RecordingOptions $options): RecordingResultInterface;
    public function buildCommand(StreamConfig $streamConfig, RecordingOptions $options, string $outputPath): array;
    public function validateEnvironment(): array;
    public function getSupportedFormats(): array;
}
```

#### 3. 配置系统
- `RecordingOptions`: 录制选项配置
- `StreamConfig`: 流配置信息
- 支持不可变更新和验证

#### 4. 工厂模式
根据需求创建合适的录制器：

```php
// 标准录制器
$recorder = RecorderFactory::create();

// 海外平台优化录制器
$recorder = RecorderFactory::createForOverseas();

// 根据 URL 自动选择
$recorder = RecorderFactory::createForUrl($url);
```

## 🔧 高级用法

### 环境验证

```php
$manager = LiveStreamManager::create();
$errors = $manager->validateEnvironment();

if (!empty($errors)) {
    foreach ($errors as $error) {
        echo "错误: {$error}\n";
    }
}
```

### 预览录制信息

```php
$preview = $manager->preview($url, $options);
if ($preview['status'] === 'ready') {
    echo "主播: {$preview['room_info']['anchor_name']}\n";
    echo "支持格式: " . implode(', ', $preview['supported_formats']) . "\n";
}
```

### 配置修改

```php
$options = new RecordingOptions(quality: Quality::ORIGINAL);

// 创建修改版本（原对象不变）
$newOptions = $options->with(
    quality: Quality::HIGH,
    format: OutputFormat::MKV
);
```

### 自定义路径构建

```php
use LiveStream\Utils\PathBuilder;

$pathBuilder = new PathBuilder();
$recorder = RecorderFactory::createWithPathBuilder($pathBuilder);
```

## 🎛️ 配置选项

### RecordingOptions

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| quality | Quality | ORIGINAL | 录制画质 |
| format | OutputFormat | MP4 | 输出格式 |
| savePath | ?string | null | 保存路径 |
| filenameTemplate | ?string | null | 文件名模板 |
| splitTime | ?int | null | 分段时间（秒） |
| proxy | ?string | null | 代理地址 |
| createFolderByPlatform | bool | true | 按平台创建文件夹 |
| createFolderByAuthor | bool | false | 按主播创建文件夹 |
| createFolderByDate | bool | false | 按日期创建文件夹 |
| enableHttps | bool | true | 启用 HTTPS |
| timeoutSeconds | int | 300 | 超时时间 |
| maxRetries | int | 3 | 最大重试次数 |

### 画质选项

- `Quality::ORIGINAL` - 原画
- `Quality::ULTRA_HIGH` - 超清
- `Quality::HIGH` - 高清
- `Quality::STANDARD` - 标清
- `Quality::LOW` - 流畅

### 格式选项

- `OutputFormat::MP4` - MP4 视频
- `OutputFormat::MKV` - Matroska 视频
- `OutputFormat::TS` - MPEG-TS 传输流
- `OutputFormat::FLV` - Flash 视频
- `OutputFormat::MP3` - MP3 音频
- `OutputFormat::AAC` - AAC 音频

## 🔒 类型安全

本扩展包充分利用 PHP 8.1+ 的类型系统：

- 严格类型模式 (`declare(strict_types=1)`)
- 构造函数属性提升
- 只读属性 (`readonly`)
- 枚举类型 (`enum`)
- 联合类型
- 命名参数

## 🧪 测试

```bash
# 运行测试
composer test

# 代码风格检查
composer cs-check

# 静态分析
composer stan
```

## 📦 与旧版本的兼容性

新架构提供向后兼容的适配器：

```php
// 旧方式仍然可用
$options = RecordingOptions::fromArray([
    'quality' => '原画',
    'format' => 'mp4',
    'save_path' => './recordings',
]);

// 转换为数组（如果需要）
$array = $options->toArray();
```

## 🤝 贡献

欢迎贡献代码！请遵循以下原则：

1. 遵循 PSR-12 编码规范
2. 添加类型提示和 PHPDoc
3. 编写测试用例
4. 保持接口的向后兼容性

## 📄 许可证

MIT License