<?php

declare(strict_types=1);

use LiveStream\Config\RecordingOptions;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;
use LiveStream\Factory\RecorderFactory;
use LiveStream\LiveStreamManager;
use LiveStream\Recording\StreamRecorder;

test('配置对象创建和验证', function () {
    $options = new RecordingOptions(
        quality: Quality::ORIGINAL,
        format: OutputFormat::MP4,
        savePath: './test-recordings',
    );

    expect($options->quality)->toBe(Quality::ORIGINAL);
    expect($options->format)->toBe(OutputFormat::MP4);
    expect($options->getSavePath())->toBe('./test-recordings');
    expect($options->validate())->toBeEmpty();
});

test('配置对象不可变更新', function () {
    $original = new RecordingOptions(quality: Quality::ORIGINAL);
    $modified = $original->with(quality: Quality::HIGH);

    expect($original->quality)->toBe(Quality::ORIGINAL);
    expect($modified->quality)->toBe(Quality::HIGH);
});

test('从数组创建配置', function () {
    $options = RecordingOptions::fromArray([
        'quality' => '原画',
        'format' => 'mp4',
        'save_path' => './recordings',
    ]);

    expect($options->quality)->toBe(Quality::ORIGINAL);
    expect($options->format)->toBe(OutputFormat::MP4);
    expect($options->getSavePath())->toBe('./recordings');
});

test('枚举类型功能', function () {
    expect(Quality::ORIGINAL->getDisplayName())->toBe('原画');
    expect(Quality::fromDisplayName('原画'))->toBe(Quality::ORIGINAL);
    expect(OutputFormat::MP4->isVideo())->toBeTrue();
    expect(OutputFormat::MP3->isAudio())->toBeTrue();
});

test('工厂模式创建录制器', function () {
    $standardRecorder = RecorderFactory::create();
    $overseasRecorder = RecorderFactory::createForOverseas();
    $autoRecorder = RecorderFactory::createForUrl('https://www.tiktok.com/@user/live');

    expect($standardRecorder)->toBeInstanceOf(StreamRecorder::class);
    expect($overseasRecorder)->toBeInstanceOf(StreamRecorder::class);
    expect($autoRecorder)->toBeInstanceOf(StreamRecorder::class);
});

test('管理器创建和基础功能', function () {
    $manager = LiveStreamManager::create();

    expect($manager)->toBeInstanceOf(LiveStreamManager::class);

    $supportedPlatforms = $manager->getSupportedPlatforms();
    expect($supportedPlatforms)->toHaveKey('douyin');
    expect($supportedPlatforms['douyin']['name'])->toBe('抖音直播');
});

test('环境验证', function () {
    $manager = LiveStreamManager::create();
    $errors = $manager->validateEnvironment();

    // 验证结果应该是数组
    expect($errors)->toBeArray();

    // 如果 FFmpeg 未安装，应该有错误
    if (!empty($errors)) {
        expect($errors[0])->toContain('FFmpeg');
    }
});

test('录制器接口实现', function () {
    $recorder = RecorderFactory::create();

    expect($recorder->getSupportedFormats())->toBeArray();
    expect($recorder->getSupportedFormats())->toContain('mp4');
    expect($recorder->validateEnvironment())->toBeArray();
});

test('海外平台检测', function () {
    expect(StreamRecorder::isOverseasPlatform('https://www.tiktok.com/@user/live'))->toBeTrue();
    expect(StreamRecorder::isOverseasPlatform('https://live.douyin.com/123'))->toBeFalse();
    expect(StreamRecorder::isOverseasPlatform('https://www.twitch.tv/user'))->toBeTrue();
});

test('配置验证错误', function () {
    $options = new RecordingOptions(
        splitTime: -1, // 无效值
        timeoutSeconds: 0, // 无效值
        proxy: 'invalid-url' // 无效代理
    );

    $errors = $options->validate();
    expect($errors)->not->toBeEmpty();
    expect(implode(', ', $errors))->toContain('Split time must be positive');
    expect(implode(', ', $errors))->toContain('Timeout seconds must be positive');
    expect(implode(', ', $errors))->toContain('Proxy must be a valid URL');
});
