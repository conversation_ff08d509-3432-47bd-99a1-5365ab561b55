<?php

declare(strict_types=1);


use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Platforms\Douyin\DouyinPlatform;
use LiveStream\Platforms\Douyin\RoomInfo\DouyinRoomInfo;
use LiveStream\Platforms\Douyin\Http\Connector\DouyinConnector;
beforeEach(function () {
    $this->liveStream = new LiveStream(new PlatformFactory());
});

test('test get live data', function () {
    
    $driver = $this->liveStream->driver('https://live.douyin.com/679309292214');


    expect($driver)->toBeInstanceOf(DouyinPlatform::class);
    expect($driver->getRoomInfo())->toBeInstanceOf(DouyinRoomInfo::class);
    expect($driver->getRoomInfo()->getStreamUrls())->toBeArray()->dump();
});


test('test get live data html', function () {
    

    $connector = new DouyinConnector();

    $data = $connector->resource()->parseWebRoomFromHtml(file_get_contents(__DIR__.'/html.html'));

    expect($data)->toBeArray();
});
