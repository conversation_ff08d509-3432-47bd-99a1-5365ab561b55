<?php

declare(strict_types=1);

namespace LiveStream\Tests\Unit;

use LiveStream\LiveStream;
use LiveStream\Exceptions\PlatformException;
use PHPUnit\Framework\TestCase;

class LiveStreamTest extends TestCase
{
    private LiveStream $liveStream;

    protected function setUp(): void
    {
        $this->liveStream = new LiveStream();
    }

    public function testGetSupportedPlatforms(): void
    {
        $platforms = $this->liveStream->getSupportedPlatforms();

        $this->assertIsArray($platforms);
        $this->assertContains('抖音', $platforms);
    }

    public function testUnsupportedUrl(): void
    {
        $this->expectException(PlatformException::class);

        $this->liveStream->getLiveData('https://example.com/live');
    }

    public function testDouyinUrlSupport(): void
    {
        $url = 'https://live.douyin.com/123456789';

        // 这里应该能识别为抖音URL，但由于没有真实的网络请求，会抛出异常
        $this->expectException(\Exception::class);

        $this->liveStream->getLiveData($url);
    }
}
