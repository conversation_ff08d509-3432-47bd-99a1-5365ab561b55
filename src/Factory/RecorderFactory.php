<?php

declare(strict_types=1);

namespace LiveStream\Factory;

use LiveStream\Contracts\RecorderInterface;
use LiveStream\Recording\StreamRecorder;
use LiveStream\Utils\PathBuilder;

/**
 * 录制器工厂
 * 
 * 负责创建合适的录制器实例
 */
final class RecorderFactory
{
    /**
     * 创建标准录制器
     */
    public static function create(): RecorderInterface
    {
        return new StreamRecorder(new PathBuilder());
    }

    /**
     * 创建海外平台录制器
     */
    public static function createForOverseas(): RecorderInterface
    {
        return StreamRecorder::forOverseas();
    }

    /**
     * 根据 URL 自动选择录制器
     */
    public static function createForUrl(string $url): RecorderInterface
    {
        if (StreamRecorder::isOverseasPlatform($url)) {
            return self::createForOverseas();
        }

        return self::create();
    }

    /**
     * 创建带自定义路径构建器的录制器
     */
    public static function createWithPathBuilder(PathBuilder $pathBuilder, bool $overseas = false): RecorderInterface
    {
        return new StreamRecorder($pathBuilder, $overseas);
    }
}
