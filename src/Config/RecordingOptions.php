<?php

declare(strict_types=1);

namespace LiveStream\Config;

use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;

/**
 * 录制选项配置类
 * 
 * 封装录制相关的所有配置选项，替换原有的数组配置
 */
final readonly class RecordingOptions
{
    public function __construct(
        public Quality $quality = Quality::ORIGINAL,
        public OutputFormat $format = OutputFormat::MP4,
        public ?string $savePath = null,
        public ?string $filenameTemplate = null,
        public ?int $splitTime = null,
        public ?string $proxy = null,
        public bool $createFolderByPlatform = true,
        public bool $createFolderByAuthor = false,
        public bool $createFolderByDate = false,
        public bool $enableHttps = true,
        public int $timeoutSeconds = 300,
        public int $maxRetries = 3,
        public array $customHeaders = [],
        public array $ffmpegOptions = [],
    ) {}

    /**
     * 从数组创建配置选项
     */
    public static function fromArray(array $options): self
    {
        return new self(
            quality: isset($options['quality'])
                ? (Quality::fromDisplayName($options['quality']) ?? Quality::ORIGINAL)
                : Quality::ORIGINAL,
            format: isset($options['format'])
                ? (OutputFormat::tryFrom($options['format']) ?? OutputFormat::MP4)
                : OutputFormat::MP4,
            savePath: $options['save_path'] ?? null,
            filenameTemplate: $options['filename_template'] ?? null,
            splitTime: $options['split_time'] ?? null,
            proxy: $options['proxy'] ?? null,
            createFolderByPlatform: $options['create_folder_by_platform'] ?? true,
            createFolderByAuthor: $options['create_folder_by_author'] ?? false,
            createFolderByDate: $options['create_folder_by_date'] ?? false,
            enableHttps: $options['enable_https'] ?? true,
            timeoutSeconds: $options['timeout'] ?? 300,
            maxRetries: $options['max_retries'] ?? 3,
            customHeaders: $options['custom_headers'] ?? [],
            ffmpegOptions: $options['ffmpeg_options'] ?? [],
        );
    }

    /**
     * 转换为数组（向后兼容）
     */
    public function toArray(): array
    {
        return [
            'quality' => $this->quality->getDisplayName(),
            'format' => $this->format->value,
            'save_path' => $this->savePath,
            'filename_template' => $this->filenameTemplate,
            'split_time' => $this->splitTime,
            'proxy' => $this->proxy,
            'create_folder_by_platform' => $this->createFolderByPlatform,
            'create_folder_by_author' => $this->createFolderByAuthor,
            'create_folder_by_date' => $this->createFolderByDate,
            'enable_https' => $this->enableHttps,
            'timeout' => $this->timeoutSeconds,
            'max_retries' => $this->maxRetries,
            'custom_headers' => $this->customHeaders,
            'ffmpeg_options' => $this->ffmpegOptions,
        ];
    }

    /**
     * 获取有效的文件名模板
     */
    public function getFilenameTemplate(): string
    {
        return $this->filenameTemplate ?? '{anchor_name}_{title}_{datetime}';
    }

    /**
     * 获取有效的保存路径
     */
    public function getSavePath(): string
    {
        return $this->savePath ?? './recordings';
    }

    /**
     * 验证配置选项的有效性
     * 
     * @return array<string> 验证错误列表
     */
    public function validate(): array
    {
        $errors = [];

        if ($this->splitTime !== null && $this->splitTime <= 0) {
            $errors[] = 'Split time must be positive integer or null';
        }

        if ($this->timeoutSeconds <= 0) {
            $errors[] = 'Timeout seconds must be positive integer';
        }

        if ($this->maxRetries < 0) {
            $errors[] = 'Max retries must be non-negative integer';
        }

        if ($this->proxy !== null && !filter_var($this->proxy, FILTER_VALIDATE_URL)) {
            $errors[] = 'Proxy must be a valid URL';
        }

        return $errors;
    }

    /**
     * 创建一个带有修改选项的新实例
     */
    public function with(
        ?Quality $quality = null,
        ?OutputFormat $format = null,
        ?string $savePath = null,
        ?string $filenameTemplate = null,
        ?int $splitTime = null,
        ?string $proxy = null,
        ?bool $createFolderByPlatform = null,
        ?bool $createFolderByAuthor = null,
        ?bool $createFolderByDate = null,
        ?bool $enableHttps = null,
        ?int $timeoutSeconds = null,
        ?int $maxRetries = null,
        ?array $customHeaders = null,
        ?array $ffmpegOptions = null,
    ): self {
        return new self(
            quality: $quality ?? $this->quality,
            format: $format ?? $this->format,
            savePath: $savePath ?? $this->savePath,
            filenameTemplate: $filenameTemplate ?? $this->filenameTemplate,
            splitTime: $splitTime ?? $this->splitTime,
            proxy: $proxy ?? $this->proxy,
            createFolderByPlatform: $createFolderByPlatform ?? $this->createFolderByPlatform,
            createFolderByAuthor: $createFolderByAuthor ?? $this->createFolderByAuthor,
            createFolderByDate: $createFolderByDate ?? $this->createFolderByDate,
            enableHttps: $enableHttps ?? $this->enableHttps,
            timeoutSeconds: $timeoutSeconds ?? $this->timeoutSeconds,
            maxRetries: $maxRetries ?? $this->maxRetries,
            customHeaders: $customHeaders ?? $this->customHeaders,
            ffmpegOptions: $ffmpegOptions ?? $this->ffmpegOptions,
        );
    }
}
