<?php

declare(strict_types=1);

namespace LiveStream;

use LiveStream\Contracts\PlatformInterface;
use LiveStream\Platforms\DouyinPlatform;
use LiveStream\Exceptions\PlatformException;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * 直播流数据获取主类
 * 
 * 提供统一的接口来获取各个直播平台的数据
 */
class LiveStream
{
    private array $platforms = [];
    private LoggerInterface $logger;

    public function __construct(?LoggerInterface $logger = null)
    {
        $this->logger = $logger ?? new NullLogger();
        $this->registerDefaultPlatforms();
    }

    /**
     * 注册默认平台
     */
    private function registerDefaultPlatforms(): void
    {
        $this->registerPlatform(new DouyinPlatform($this->logger));
        // 后续可以添加更多平台
        // $this->registerPlatform(new TikTokPlatform($this->logger));
        // $this->registerPlatform(new KuaishouPlatform($this->logger));
        // $this->registerPlatform(new BilibiliPlatform($this->logger));
    }

    /**
     * 注册平台
     * 
     * @param PlatformInterface $platform 平台实例
     */
    public function registerPlatform(PlatformInterface $platform): void
    {
        $this->platforms[] = $platform;
        $this->logger->info('Platform registered', [
            'platform' => $platform->getPlatformName()
        ]);
    }

    /**
     * 获取支持的平台列表
     * 
     * @return array 平台名称列表
     */
    public function getSupportedPlatforms(): array
    {
        return array_map(fn($platform) => $platform->getPlatformName(), $this->platforms);
    }

    /**
     * 获取直播数据
     * 
     * @param string $url 直播间URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 直播数据
     * @throws PlatformException
     */
    public function getLiveData(string $url, ?string $proxy = null, ?string $cookies = null): array
    {
        $platform = $this->findPlatform($url);

        if (!$platform) {
            throw PlatformException::unsupportedUrl($url, 'Unknown');
        }

        $this->logger->info('Getting live data', [
            'url' => $url,
            'platform' => $platform->getPlatformName(),
            'proxy' => $proxy ? 'enabled' : 'disabled'
        ]);

        return $platform->getLiveData($url, $proxy, $cookies);
    }

    /**
     * 获取流地址
     * 
     * @param string $url 直播间URL
     * @param string $quality 画质
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 流地址信息
     * @throws PlatformException
     */
    public function getStreamUrl(string $url, string $quality = '原画', ?string $proxy = null, ?string $cookies = null): array
    {
        $platform = $this->findPlatform($url);

        if (!$platform) {
            throw PlatformException::unsupportedUrl($url, 'Unknown');
        }

        $liveData = $platform->getLiveData($url, $proxy, $cookies);

        if (!$platform->isLive($liveData)) {
            throw PlatformException::notLive($platform->getPlatformName(), $url);
        }

        $streamData = $platform->getStreamUrl($liveData, $quality, $proxy);

        $this->logger->info('Stream URL obtained', [
            'url' => $url,
            'platform' => $platform->getPlatformName(),
            'quality' => $quality,
            'is_live' => $streamData['is_live'] ?? false
        ]);

        return $streamData;
    }

    /**
     * 检查是否正在直播
     * 
     * @param string $url 直播间URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return bool 是否正在直播
     * @throws PlatformException
     */
    public function isLive(string $url, ?string $proxy = null, ?string $cookies = null): bool
    {
        $platform = $this->findPlatform($url);

        if (!$platform) {
            throw PlatformException::unsupportedUrl($url, 'Unknown');
        }

        $liveData = $platform->getLiveData($url, $proxy, $cookies);
        return $platform->isLive($liveData);
    }

    /**
     * 获取主播信息
     * 
     * @param string $url 直播间URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 主播信息
     * @throws PlatformException
     */
    public function getAnchorInfo(string $url, ?string $proxy = null, ?string $cookies = null): array
    {
        $platform = $this->findPlatform($url);

        if (!$platform) {
            throw PlatformException::unsupportedUrl($url, 'Unknown');
        }

        $liveData = $platform->getLiveData($url, $proxy, $cookies);

        return [
            'platform' => $platform->getPlatformName(),
            'anchor_name' => $platform->getAnchorName($liveData),
            'title' => $platform->getTitle($liveData),
            'is_live' => $platform->isLive($liveData)
        ];
    }

    /**
     * 查找支持该URL的平台
     * 
     * @param string $url 直播间URL
     * @return PlatformInterface|null 平台实例
     */
    private function findPlatform(string $url): ?PlatformInterface
    {
        foreach ($this->platforms as $platform) {
            if ($platform->supportsUrl($url)) {
                return $platform;
            }
        }

        return null;
    }

    /**
     * 批量检查直播状态
     * 
     * @param array $urls URL列表
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 直播状态列表
     */
    public function batchCheckLiveStatus(array $urls, ?string $proxy = null, ?string $cookies = null): array
    {
        $results = [];

        foreach ($urls as $url) {
            try {
                $results[$url] = [
                    'is_live' => $this->isLive($url, $proxy, $cookies),
                    'anchor_info' => $this->getAnchorInfo($url, $proxy, $cookies)
                ];
            } catch (\Exception $e) {
                $results[$url] = [
                    'is_live' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}
