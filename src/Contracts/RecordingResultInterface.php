<?php

declare(strict_types=1);

namespace LiveStream\Contracts;

use LiveStream\Config\RecordingOptions;
use LiveStream\Config\StreamConfig;

/**
 * 录制结果接口
 * 
 * 封装录制操作的结果信息
 */
interface RecordingResultInterface
{
    /**
     * 录制是否成功
     */
    public function isSuccessful(): bool;

    /**
     * 获取录制的输出文件路径
     */
    public function getOutputPath(): string;

    /**
     * 获取录制的 FFmpeg 命令
     * 
     * @return array<string>
     */
    public function getCommand(): array;

    /**
     * 获取录制配置选项
     */
    public function getOptions(): RecordingOptions;

    /**
     * 获取流配置
     */
    public function getStreamConfig(): StreamConfig;

    /**
     * 获取房间信息
     */
    public function getRoomInfo(): RoomInfoInterface;

    /**
     * 获取错误信息（如果录制失败）
     */
    public function getError(): ?string;

    /**
     * 获取录制统计信息
     * 
     * @return array<string, mixed>
     */
    public function getMetadata(): array;

    /**
     * 转换为数组表示
     * 
     * @return array<string, mixed>
     */
    public function toArray(): array;
}
