<?php

declare(strict_types=1);

namespace LiveStream\Contracts;

/**
 * 直播平台基础接口
 *
 * 定义所有直播平台必须实现的方法，基于DouyinLiveRecorder项目的功能需求设计
 */
interface PlatformInterface
{
    // ==================== 基础平台信息 ====================

    /**
     * 获取平台名称
     */
    public function getPlatformName(): string;

    /**
     * 获取平台标识符（用于配置和日志）
     */
    public function getPlatformIdentifier(): string;

    /**
     * 检查URL是否属于该平台
     */
    public function supportsUrl(string $url): bool;

    /**
     * 获取支持的URL模式列表
     *
     * @return array URL正则表达式模式数组
     */
    public function getSupportedUrlPatterns(): array;

    // ==================== URL处理和解析 ====================

    /**
     * 标准化URL（处理短链接、重定向等）
     *
     * @param string $url 原始URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return string 标准化后的URL
     * @throws \LiveStream\Exceptions\PlatformException
     */
    public function normalizeUrl(string $url, ?string $proxy = null, ?string $cookies = null): string;

    /**
     * 从URL中提取房间ID
     *
     * @param string $url 直播间URL
     * @return string 房间ID
     * @throws \LiveStream\Exceptions\PlatformException
     */
    public function extractRoomId(string $url): string;

    /**
     * 从URL中提取用户ID（如果适用）
     *
     * @param string $url 直播间URL
     * @return string|null 用户ID
     */
    public function extractUserId(string $url): ?string;

    // ==================== 直播数据获取 ====================

    /**
     * 获取直播数据
     *
     * @param string $url 直播间URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 直播数据
     * @throws \LiveStream\Exceptions\PlatformException
     */
    public function getLiveData(string $url, ?string $proxy = null, ?string $cookies = null): array;

    /**
     * 获取直播数据（移动端API方法）
     *
     * @param string $url 直播间URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 直播数据
     * @throws \LiveStream\Exceptions\PlatformException
     */
    public function getLiveDataMobile(string $url, ?string $proxy = null, ?string $cookies = null): array;

    // ==================== 直播状态检查 ====================

    /**
     * 检查是否正在直播
     */
    public function isLive(array $data): bool;

    /**
     * 获取直播状态码
     *
     * @param array $data 直播数据
     * @return int 状态码（2=直播中，4=未直播等）
     */
    public function getLiveStatus(array $data): int;

    /**
     * 获取直播状态描述
     *
     * @param array $data 直播数据
     * @return string 状态描述
     */
    public function getLiveStatusDescription(array $data): string;

    // ==================== 主播和房间信息 ====================

    /**
     * 获取主播名称
     */
    public function getAnchorName(array $data): string;

    /**
     * 获取主播ID
     *
     * @param array $data 直播数据
     * @return string 主播ID
     */
    public function getAnchorId(array $data): string;

    /**
     * 获取主播头像URL
     *
     * @param array $data 直播数据
     * @return string|null 头像URL
     */
    public function getAnchorAvatar(array $data): ?string;

    /**
     * 获取直播标题
     */
    public function getTitle(array $data): string;

    /**
     * 获取房间ID
     *
     * @param array $data 直播数据
     * @return string 房间ID
     */
    public function getRoomId(array $data): string;

    /**
     * 获取观看人数
     *
     * @param array $data 直播数据
     * @return int 观看人数
     */
    public function getViewerCount(array $data): int;

    /**
     * 获取直播分类/标签
     *
     * @param array $data 直播数据
     * @return array 分类标签数组
     */
    public function getCategories(array $data): array;

    // ==================== 流地址处理 ====================

    /**
     * 获取流地址
     *
     * @param array $data 直播数据
     * @param string $quality 画质 (原画|超清|高清|标清|流畅)
     * @param string|null $proxy 代理地址
     * @return array 流地址信息
     */
    public function getStreamUrl(array $data, string $quality = '原画', ?string $proxy = null): array;

    /**
     * 获取所有可用画质列表
     *
     * @param array $data 直播数据
     * @return array 画质列表 ['原画', '超清', '高清', '标清', '流畅']
     */
    public function getAvailableQualities(array $data): array;

    /**
     * 获取指定画质的流地址
     *
     * @param array $data 直播数据
     * @param string $quality 画质
     * @param string $format 格式 (m3u8|flv|rtmp)
     * @param string|null $proxy 代理地址
     * @return string|null 流地址
     */
    public function getStreamUrlByQuality(array $data, string $quality, string $format = 'm3u8', ?string $proxy = null): ?string;

    /**
     * 验证流地址有效性
     *
     * @param string $url 流地址
     * @param string|null $proxy 代理地址
     * @return bool 是否有效
     */
    public function validateStreamUrl(string $url, ?string $proxy = null): bool;

    /**
     * 获取最佳流地址（自动选择最高可用画质）
     *
     * @param array $data 直播数据
     * @param string|null $proxy 代理地址
     * @return array 最佳流地址信息
     */
    public function getBestStreamUrl(array $data, ?string $proxy = null): array;

    // ==================== 画质和格式处理 ====================

    /**
     * 获取画质映射表
     *
     * @return array 画质映射 ['原画' => 0, '超清' => 1, ...]
     */
    public function getQualityMapping(): array;

    /**
     * 获取画质索引
     *
     * @param string $quality 画质名称
     * @return int 画质索引
     */
    public function getQualityIndex(string $quality): int;

    /**
     * 获取支持的流格式
     *
     * @return array 支持的格式 ['m3u8', 'flv', 'rtmp']
     */
    public function getSupportedFormats(): array;

    /**
     * 格式化流地址（添加必要的参数）
     *
     * @param string $url 原始流地址
     * @param array $params 额外参数
     * @return string 格式化后的流地址
     */
    public function formatStreamUrl(string $url, array $params = []): string;

    // ==================== 反爬虫和安全 ====================

    /**
     * 生成请求签名（如X-Bogus等）
     *
     * @param string $url 请求URL
     * @param string $userAgent User-Agent
     * @param array $params 额外参数
     * @return array 签名信息 ['X-Bogus' => 'xxx', ...]
     */
    public function generateSignature(string $url, string $userAgent, array $params = []): array;

    /**
     * 获取默认请求头
     *
     * @return array 默认请求头
     */
    public function getDefaultHeaders(): array;

    /**
     * 获取平台特定的Cookie要求
     *
     * @return array Cookie要求说明
     */
    public function getCookieRequirements(): array;

    /**
     * 处理反爬虫检测
     *
     * @param string $response 响应内容
     * @param string $url 请求URL
     * @return bool 是否需要重试
     */
    public function handleAntiBot(string $response, string $url): bool;

    // ==================== 错误处理和重试 ====================

    /**
     * 检查响应是否有效
     *
     * @param string $response 响应内容
     * @param string $url 请求URL
     * @return bool 是否有效
     */
    public function isValidResponse(string $response, string $url): bool;

    /**
     * 获取重试配置
     *
     * @return array 重试配置 ['max_retries' => 3, 'delay' => 1000, ...]
     */
    public function getRetryConfig(): array;

    /**
     * 处理请求失败
     *
     * @param \Exception $exception 异常信息
     * @param string $url 请求URL
     * @param int $attempt 当前尝试次数
     * @return bool 是否应该重试
     */
    public function shouldRetry(\Exception $exception, string $url, int $attempt): bool;

    // ==================== 数据缓存和优化 ====================

    /**
     * 获取缓存键
     *
     * @param string $url 直播间URL
     * @param array $params 额外参数
     * @return string 缓存键
     */
    public function getCacheKey(string $url, array $params = []): string;

    /**
     * 获取缓存过期时间（秒）
     *
     * @return int 缓存过期时间
     */
    public function getCacheTtl(): int;

    /**
     * 是否支持增量更新
     *
     * @return bool 是否支持
     */
    public function supportsIncrementalUpdate(): bool;

    // ==================== 平台特性支持 ====================

    /**
     * 是否支持移动端API
     *
     * @return bool 是否支持
     */
    public function supportsMobileApi(): bool;

    /**
     * 是否支持原画质
     *
     * @return bool 是否支持
     */
    public function supportsOriginQuality(): bool;

    /**
     * 是否需要登录
     *
     * @return bool 是否需要
     */
    public function requiresLogin(): bool;

    /**
     * 是否支持代理
     *
     * @return bool 是否支持
     */
    public function supportsProxy(): bool;

    /**
     * 获取平台限制信息
     *
     * @return array 限制信息 ['rate_limit' => 60, 'concurrent_limit' => 5, ...]
     */
    public function getPlatformLimits(): array;

    // ==================== 扩展功能 ====================

    /**
     * 获取直播历史记录（如果支持）
     *
     * @param string $url 直播间URL
     * @param int $limit 记录数量限制
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 历史记录
     */
    public function getLiveHistory(string $url, int $limit = 10, ?string $proxy = null, ?string $cookies = null): array;

    /**
     * 获取相关推荐直播间（如果支持）
     *
     * @param string $url 直播间URL
     * @param int $limit 推荐数量限制
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 推荐列表
     */
    public function getRecommendedStreams(string $url, int $limit = 10, ?string $proxy = null, ?string $cookies = null): array;

    /**
     * 获取平台统计信息
     *
     * @return array 统计信息
     */
    public function getPlatformStats(): array;
}
