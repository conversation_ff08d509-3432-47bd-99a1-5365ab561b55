<?php

declare(strict_types=1);

namespace LiveStream\Contracts;

/**
 * 直播平台基础接口
 * 
 * 定义所有直播平台必须实现的方法
 */
interface PlatformInterface
{
    /**
     * 获取平台名称
     */
    public function getPlatformName(): string;

    /**
     * 检查URL是否属于该平台
     */
    public function supportsUrl(string $url): bool;

    /**
     * 获取直播数据
     * 
     * @param string $url 直播间URL
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return array 直播数据
     * @throws \LiveStream\Exceptions\PlatformException
     */
    public function getLiveData(string $url, ?string $proxy = null, ?string $cookies = null): array;

    /**
     * 检查是否正在直播
     */
    public function isLive(array $data): bool;

    /**
     * 获取主播名称
     */
    public function getAnchorName(array $data): string;

    /**
     * 获取直播标题
     */
    public function getTitle(array $data): string;

    /**
     * 获取流地址
     * 
     * @param array $data 直播数据
     * @param string $quality 画质 (原画|超清|高清|标清|流畅)
     * @param string|null $proxy 代理地址
     * @return array 流地址信息
     */
    public function getStreamUrl(array $data, string $quality = '原画', ?string $proxy = null): array;
}
