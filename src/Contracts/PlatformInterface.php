<?php

declare(strict_types=1);

namespace LiveStream\Contracts;

/**
 * 直播平台基础接口
 *
 * 基于DouyinLiveRecorder项目main.py的实际录制流程设计
 * 确保接口与Python版本的业务逻辑保持一致
 */
interface PlatformInterface
{
    // ==================== 基础平台信息 ====================

    /**
     * 获取平台名称（对应main.py中的platform变量）
     *
     * @return string 平台名称，如：'抖音直播'、'快手直播'、'B站直播'等
     */
    public function getPlatformName(): string;

    /**
     * 获取平台标识符（用于配置和日志）
     *
     * @return string 平台标识符，如：'douyin'、'kuaishou'、'bilibili'等
     */
    public function getPlatformIdentifier(): string;

    /**
     * 检查URL是否属于该平台（对应main.py中的URL匹配逻辑）
     *
     * @param string $url 直播间URL
     * @return bool 是否支持该URL
     */
    public function supportsUrl(string $url): bool;

    // ==================== 核心录制流程方法（基于main.py逻辑） ====================

    /**
     * 获取房间信息（对应main.py中spider模块调用）
     *
     * 这是录制流程的第一步，获取直播间的基础信息
     * 对应Python版本中的：spider.get_douyin_stream_data()等方法
     *
     * @return RoomInfoInterface 房间信息对象
     * @throws \LiveStream\Exceptions\PlatformException
     */
    public function getRoomInfo(): RoomInfoInterface;
}
