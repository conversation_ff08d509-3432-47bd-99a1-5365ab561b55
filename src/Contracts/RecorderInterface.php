<?php

declare(strict_types=1);

namespace LiveStream\Contracts;

use LiveStream\Config\RecordingOptions;
use LiveStream\Config\StreamConfig;

/**
 * 录制器接口
 * 
 * 定义录制器的核心功能，专注于扩展包的核心职责
 */
interface RecorderInterface
{
    /**
     * 开始录制直播流
     * 
     * @param RoomInfoInterface $roomInfo 房间信息对象
     * @param RecordingOptions $options 录制选项配置
     * @return RecordingResultInterface 录制结果
     */
    public function record(RoomInfoInterface $roomInfo, RecordingOptions $options): RecordingResultInterface;

    /**
     * 构建录制命令
     * 
     * @param StreamConfig $streamConfig 流配置
     * @param RecordingOptions $options 录制选项
     * @param string $outputPath 输出路径
     * @return array FFmpeg 命令数组
     */
    public function buildCommand(StreamConfig $streamConfig, RecordingOptions $options, string $outputPath): array;

    /**
     * 验证录制环境
     * 
     * @return array<string> 验证错误列表，空数组表示验证通过
     */
    public function validateEnvironment(): array;

    /**
     * 获取支持的输出格式
     * 
     * @return array<string> 支持的格式列表
     */
    public function getSupportedFormats(): array;
}
