<?php

declare(strict_types=1);

namespace LiveStream\Utils;

/**
 * X-Bogus签名生成器
 * 
 * 用于生成抖音API请求的X-Bogus签名
 */
class XBogusGenerator
{
    private string $nodePath;
    private string $scriptPath;

    public function __construct()
    {
        $this->nodePath = $this->findNodePath();
        $this->scriptPath = __DIR__ . '/../../javascript/x-bogus.js';
    }

    /**
     * 生成X-Bogus签名
     * 
     * @param string $url 请求URL
     * @param string $userAgent User-Agent字符串
     * @return string X-Bogus签名
     * @throws \Exception
     */
    public function generate(string $url, string $userAgent): string
    {
        if (!$this->isNodeAvailable()) {
            throw new \Exception('Node.js is not available. Please install Node.js to use X-Bogus signature generation.');
        }

        if (!file_exists($this->scriptPath)) {
            throw new \Exception("X-Bogus script not found at: {$this->scriptPath}");
        }

        $query = parse_url($url, PHP_URL_QUERY) ?: '';

        // 转义参数中的特殊字符
        $query = addslashes($query);
        $userAgent = addslashes($userAgent);

        $command = sprintf(
            '%s -e "const xbogus = require(\'%s\'); console.log(xbogus.sign(\'%s\', \'%s\'));"',
            $this->nodePath,
            $this->scriptPath,
            $query,
            $userAgent
        );

        $output = shell_exec($command);

        if ($output === null) {
            throw new \Exception('Failed to execute X-Bogus generation command');
        }

        $signature = trim($output);

        if (empty($signature)) {
            throw new \Exception('X-Bogus signature generation returned empty result');
        }

        return $signature;
    }

    /**
     * 检查Node.js是否可用
     * 
     * @return bool
     */
    public function isNodeAvailable(): bool
    {
        return !empty($this->nodePath);
    }

    /**
     * 查找Node.js路径
     * 
     * @return string
     */
    private function findNodePath(): string
    {
        $possiblePaths = [
            '/usr/bin/node',
            '/usr/local/bin/node',
            '/opt/homebrew/bin/node',
            'node' // 如果在PATH中
        ];

        foreach ($possiblePaths as $path) {
            if ($this->isExecutable($path)) {
                return $path;
            }
        }

        return '';
    }

    /**
     * 检查文件是否可执行
     * 
     * @param string $path 文件路径
     * @return bool
     */
    private function isExecutable(string $path): bool
    {
        if ($path === 'node') {
            // 检查是否在PATH中
            $output = shell_exec('which node 2>/dev/null');
            return !empty($output);
        }

        return file_exists($path) && is_executable($path);
    }

    /**
     * 设置Node.js路径
     * 
     * @param string $path Node.js路径
     */
    public function setNodePath(string $path): void
    {
        $this->nodePath = $path;
    }

    /**
     * 设置脚本路径
     * 
     * @param string $path 脚本路径
     */
    public function setScriptPath(string $path): void
    {
        $this->scriptPath = $path;
    }

    /**
     * 获取Node.js路径
     * 
     * @return string
     */
    public function getNodePath(): string
    {
        return $this->nodePath;
    }

    /**
     * 获取脚本路径
     * 
     * @return string
     */
    public function getScriptPath(): string
    {
        return $this->scriptPath;
    }
}
