<?php

declare(strict_types=1);

namespace LiveStream\Utils;

use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\Config\RecordingOptions;

/**
 * 路径构建工具
 * 
 * 负责构建录制文件的路径和文件名
 */
final class PathBuilder
{
    /**
     * 构建完整的文件路径
     */
    public function buildFilePath(RoomInfoInterface $roomInfo, RecordingOptions $options): string
    {
        $basePath = $options->getSavePath();
        $platformName = $this->getPlatformName($roomInfo);

        // 构建目录路径
        $directoryPath = $this->buildDirectoryPath($basePath, $platformName, $roomInfo, $options);

        // 确保目录存在
        $this->ensureDirectoryExists($directoryPath);

        // 构建文件名
        $filename = $this->buildFilename($roomInfo, $options);

        return $directoryPath . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * 构建目录路径
     */
    private function buildDirectoryPath(
        string $basePath,
        string $platformName,
        RoomInfoInterface $roomInfo,
        RecordingOptions $options
    ): string {
        $path = $basePath;

        if ($options->createFolderByPlatform) {
            $path .= DIRECTORY_SEPARATOR . $this->sanitizeFilename($platformName);
        }

        if ($options->createFolderByAuthor) {
            $path .= DIRECTORY_SEPARATOR . $this->sanitizeFilename($roomInfo->getAnchorName());
        }

        if ($options->createFolderByDate) {
            $path .= DIRECTORY_SEPARATOR . date('Y-m-d');
        }

        return $path;
    }

    /**
     * 构建文件名
     */
    private function buildFilename(RoomInfoInterface $roomInfo, RecordingOptions $options): string
    {
        $template = $options->getFilenameTemplate();
        $datetime = date('Y-m-d_H-i-s');
        $extension = $options->format->getExtension();

        // 替换模板变量
        $filename = str_replace([
            '{anchor_name}',
            '{title}',
            '{datetime}',
            '{quality}',
            '{format}',
            '{room_id}',
        ], [
            $this->sanitizeFilename($roomInfo->getAnchorName()),
            $this->sanitizeFilename($roomInfo->getTitle()),
            $datetime,
            $options->quality->value,
            $options->format->value,
            $roomInfo->getRoomId(),
        ], $template);

        // 清理文件名
        $filename = $this->sanitizeFilename($filename);

        // 添加分段后缀（如果需要）
        if ($options->splitTime !== null) {
            $filename .= '_%03d';
        }

        return $filename . '.' . $extension;
    }

    /**
     * 清理文件名，移除非法字符
     */
    private function sanitizeFilename(string $filename): string
    {
        if (empty($filename)) {
            return 'unnamed';
        }

        // 移除或替换非法字符
        $cleaned = preg_replace('/[<>:"\\/\\\\|?*]/', '_', $filename);
        $cleaned = preg_replace('/[\x00-\x1f\x7f]/', '', $cleaned ?? '');
        $cleaned = trim($cleaned ?? '', '. ');

        return $cleaned ?: 'unnamed';
    }

    /**
     * 确保目录存在
     */
    private function ensureDirectoryExists(string $path): void
    {
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
    }

    /**
     * 获取平台名称
     */
    private function getPlatformName(RoomInfoInterface $roomInfo): string
    {
        // 根据 RoomInfo 类型判断平台
        $className = get_class($roomInfo);

        if (str_contains($className, 'Douyin')) {
            return '抖音直播';
        }

        // 可以根据需要添加更多平台
        return '未知平台';
    }

    /**
     * 生成唯一的文件路径（避免重复）
     */
    public function buildUniqueFilePath(RoomInfoInterface $roomInfo, RecordingOptions $options): string
    {
        $basePath = $this->buildFilePath($roomInfo, $options);

        if (!file_exists($basePath)) {
            return $basePath;
        }

        // 如果文件已存在，添加计数器
        $pathInfo = pathinfo($basePath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';

        $counter = 1;
        do {
            $newFilename = $filename . '_' . $counter;
            $newPath = $directory . DIRECTORY_SEPARATOR . $newFilename;
            if (!empty($extension)) {
                $newPath .= '.' . $extension;
            }
            $counter++;
        } while (file_exists($newPath));

        return $newPath;
    }
}
