<?php

declare(strict_types=1);

namespace LiveStream\Utils;

/**
 * Cookie管理器工具类
 * 
 * 用于解析和管理HTTP Cookie
 */
class CookieManager
{
    private array $cookies = [];

    /**
     * 设置Cookie字符串
     * 
     * @param string $cookies Cookie字符串
     */
    public function setCookies(string $cookies): void
    {
        $this->cookies = [];
        $pairs = explode(';', $cookies);

        foreach ($pairs as $pair) {
            $parts = explode('=', trim($pair), 2);
            if (count($parts) === 2) {
                $name = trim($parts[0]);
                $value = trim($parts[1]);
                if (!empty($name)) {
                    $this->cookies[$name] = $value;
                }
            }
        }
    }

    /**
     * 获取Cookie字符串
     * 
     * @return string Cookie字符串
     */
    public function getCookieString(): string
    {
        $pairs = [];
        foreach ($this->cookies as $name => $value) {
            $pairs[] = "{$name}={$value}";
        }
        return implode('; ', $pairs);
    }

    /**
     * 设置单个Cookie
     * 
     * @param string $name Cookie名称
     * @param string $value Cookie值
     */
    public function setCookie(string $name, string $value): void
    {
        $this->cookies[$name] = $value;
    }

    /**
     * 获取单个Cookie
     * 
     * @param string $name Cookie名称
     * @return string|null Cookie值
     */
    public function getCookie(string $name): ?string
    {
        return $this->cookies[$name] ?? null;
    }

    /**
     * 删除Cookie
     * 
     * @param string $name Cookie名称
     */
    public function removeCookie(string $name): void
    {
        unset($this->cookies[$name]);
    }

    /**
     * 清空所有Cookie
     */
    public function clearCookies(): void
    {
        $this->cookies = [];
    }

    /**
     * 获取所有Cookie
     * 
     * @return array Cookie数组
     */
    public function getAllCookies(): array
    {
        return $this->cookies;
    }

    /**
     * 检查Cookie是否存在
     * 
     * @param string $name Cookie名称
     * @return bool 是否存在
     */
    public function hasCookie(string $name): bool
    {
        return isset($this->cookies[$name]);
    }

    /**
     * 合并Cookie
     * 
     * @param array $cookies 要合并的Cookie数组
     */
    public function mergeCookies(array $cookies): void
    {
        $this->cookies = array_merge($this->cookies, $cookies);
    }
}
