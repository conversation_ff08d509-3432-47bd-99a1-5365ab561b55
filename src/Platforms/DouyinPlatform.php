<?php

declare(strict_types=1);

namespace LiveStream\Platforms;

use LiveStream\Exceptions\PlatformException;

/**
 * 抖音直播平台实现
 */
class DouyinPlatform extends AbstractPlatform
{
    protected array $supportedPatterns = [
        '/live\.douyin\.com/',
        '/v\.douyin\.com/',
        '/www\.douyin\.com/'
    ];

    public function getPlatformName(): string
    {
        return '抖音';
    }

    public function getLiveData(string $url, ?string $proxy = null, ?string $cookies = null): array
    {
        return $this->retry(function () use ($url, $proxy, $cookies) {
            try {
                return $this->getDouyinStreamData($url, $proxy, $cookies);
            } catch (\Exception $e) {
                $this->logger->warning('Primary method failed, trying app method', [
                    'url' => $url,
                    'error' => $e->getMessage()
                ]);
                return $this->getDouyinAppStreamData($url, $proxy, $cookies);
            }
        });
    }

    public function getStreamUrl(array $data, string $quality = '原画', ?string $proxy = null): array
    {
        $status = $data['status'] ?? 4;

        if ($status !== 2) {
            return ['is_live' => false];
        }

        $streamUrl = $data['stream_url'];
        $flvUrlDict = $streamUrl['flv_pull_url'] ?? [];
        $m3u8UrlDict = $streamUrl['hls_pull_url_map'] ?? [];

        $flvUrlList = array_values($flvUrlDict);
        $m3u8UrlList = array_values($m3u8UrlDict);

        // 确保列表长度足够
        while (count($flvUrlList) < 5) {
            $flvUrlList[] = end($flvUrlList) ?: '';
        }
        while (count($m3u8UrlList) < 5) {
            $m3u8UrlList[] = end($m3u8UrlList) ?: '';
        }

        $qualityIndex = $this->getQualityIndex($quality);
        $m3u8Url = $m3u8UrlList[$qualityIndex] ?? $m3u8UrlList[0];
        $flvUrl = $flvUrlList[$qualityIndex] ?? $flvUrlList[0];

        // 验证流地址有效性
        if (!$this->validateStreamUrl($m3u8Url, $proxy)) {
            $qualityIndex = min($qualityIndex + 1, count($m3u8UrlList) - 1);
            $m3u8Url = $m3u8UrlList[$qualityIndex];
            $flvUrl = $flvUrlList[$qualityIndex];
        }

        return [
            'is_live' => true,
            'title' => $data['title'] ?? '',
            'quality' => $quality,
            'm3u8_url' => $m3u8Url,
            'flv_url' => $flvUrl,
            'record_url' => $m3u8Url ?: $flvUrl,
        ];
    }

    /**
     * 获取抖音直播数据（Web端方法）
     */
    private function getDouyinStreamData(string $url, ?string $proxy, ?string $cookies): array
    {
        $headers = [
            'Referer' => 'https://live.douyin.com/',
        ];

        if ($cookies) {
            $headers['Cookie'] = $cookies;
        }

        $html = $this->makeRequest($url, $headers, $proxy);

        // 提取JSON数据
        $jsonData = $this->extractJsonData($html);
        $roomData = $this->parseRoomData($jsonData);

        // 处理原画质流地址
        $this->processOriginStream($roomData, $html);

        return $roomData;
    }

    /**
     * 获取抖音直播数据（App端方法）
     */
    private function getDouyinAppStreamData(string $url, ?string $proxy, ?string $cookies): array
    {
        $headers = [
            'Referer' => 'https://live.douyin.com/',
        ];

        if ($cookies) {
            $headers['Cookie'] = $cookies;
        }

        // 提取web_rid
        $webRid = $this->extractWebRid($url);

        if ($webRid) {
            return $this->getAppDataByWebRid($webRid, $headers, $proxy);
        } else {
            // 处理短链接或用户主页
            return $this->getAppDataByUserInfo($url, $headers, $proxy);
        }
    }

    /**
     * 提取JSON数据
     */
    private function extractJsonData(string $html): array
    {
        $patterns = [
            '/(\{\\"state\\":.*?)]\\n"]\)/',
            '/(\{\\"common\\":.*?)]\\n"]\)</script><div hidden/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $jsonStr = $matches[1];
                $cleanedString = str_replace(['\\', 'u0026'], ['', '&'], $jsonStr);

                // 提取房间信息
                if (preg_match('/"roomStore":(.*?),"linkmicStore"/', $cleanedString, $roomMatch)) {
                    $roomStore = $roomMatch[1];

                    // 提取主播名称
                    if (preg_match('/"nickname":"(.*?)","avatar_thumb/', $roomStore, $nameMatch)) {
                        $anchorName = $nameMatch[1];
                    }

                    // 构建完整的JSON
                    $roomStore = explode(',"has_commerce_goods"', $roomStore)[0] . '}}}';
                    $roomData = json_decode($roomStore, true);
                    $roomData['anchor_name'] = $anchorName ?? '未知主播';

                    return $roomData;
                }
            }
        }

        throw PlatformException::parseError('Failed to extract JSON data', $this->getPlatformName(), '');
    }

    /**
     * 解析房间数据
     */
    private function parseRoomData(array $jsonData): array
    {
        $roomInfo = $jsonData['roomInfo']['room'] ?? [];
        $roomInfo['anchor_name'] = $jsonData['anchor_name'] ?? '未知主播';

        return $roomInfo;
    }

    /**
     * 处理原画质流地址
     */
    private function processOriginStream(array &$roomData, string $html): void
    {
        if (!isset($roomData['stream_url'])) {
            return;
        }

        $streamOrientation = $roomData['stream_url']['stream_orientation'] ?? 0;

        // 查找原画质数据
        $patterns = [
            '/"origin":\\{"main":(.*?),"dash"/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $originData = json_decode($matches[1] . '}', true);

                if ($originData) {
                    $originHlsCodec = $originData['sdk_params']['VCodec'] ?? '';
                    $originM3u8 = ['ORIGIN' => $originData['hls'] . '&codec=' . $originHlsCodec];
                    $originFlv = ['ORIGIN' => $originData['flv'] . '&codec=' . $originHlsCodec];

                    $hlsPullUrlMap = $roomData['stream_url']['hls_pull_url_map'] ?? [];
                    $flvPullUrl = $roomData['stream_url']['flv_pull_url'] ?? [];

                    $roomData['stream_url']['hls_pull_url_map'] = array_merge($originM3u8, $hlsPullUrlMap);
                    $roomData['stream_url']['flv_pull_url'] = array_merge($originFlv, $flvPullUrl);
                }
                break;
            }
        }
    }

    /**
     * 提取web_rid
     */
    private function extractWebRid(string $url): ?string
    {
        if (preg_match('/live\.douyin\.com\/([^?]+)/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * 通过web_rid获取App数据
     */
    private function getAppDataByWebRid(string $webRid, array $headers, ?string $proxy): array
    {
        $params = [
            'aid' => '6383',
            'app_name' => 'douyin_web',
            'live_id' => '1',
            'device_platform' => 'web',
            'language' => 'zh-CN',
            'browser_language' => 'zh-CN',
            'browser_platform' => 'Win32',
            'browser_name' => 'Chrome',
            'browser_version' => '116.0.0.0',
            'web_rid' => $webRid
        ];

        $api = 'https://live.douyin.com/webcast/room/web/enter/?' . http_build_query($params);
        $response = $this->makeRequest($api, $headers, $proxy);
        $jsonData = json_decode($response, true);

        if (!isset($jsonData['data']['data'][0])) {
            throw PlatformException::parseError('Invalid API response', $this->getPlatformName(), $api);
        }

        $roomData = $jsonData['data']['data'][0];
        $roomData['anchor_name'] = $jsonData['data']['user']['nickname'] ?? '未知主播';

        return $roomData;
    }

    /**
     * 通过用户信息获取App数据
     */
    private function getAppDataByUserInfo(string $url, array $headers, ?string $proxy): array
    {
        // 这里需要实现用户信息获取逻辑
        // 暂时抛出异常，后续可以完善
        throw PlatformException::unsupportedUrl($url, $this->getPlatformName());
    }

    /**
     * 获取画质索引
     */
    private function getQualityIndex(string $quality): int
    {
        $qualityMap = [
            '原画' => 0,
            '超清' => 1,
            '高清' => 2,
            '标清' => 3,
            '流畅' => 4
        ];

        return $qualityMap[$quality] ?? 0;
    }
}
