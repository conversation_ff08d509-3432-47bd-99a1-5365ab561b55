<?php

declare(strict_types=1);

namespace LiveStream\Platforms;

use LiveStream\Contracts\PlatformInterface;
use LiveStream\Http\Connector\DirectConnector;
use LiveStream\Http\Requests\RawGetRequest;
use LiveStream\Http\Requests\RawHeadRequest;
use LiveStream\Utils\CookieManager;
use LiveStream\Utils\XBogusGenerator;
use LiveStream\Exceptions\PlatformException;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

/**
 * 直播平台抽象基类
 * 
 * 提供通用的HTTP请求、Cookie管理、错误处理等功能
 */
abstract class AbstractPlatform implements PlatformInterface
{
    protected DirectConnector $connector;
    protected CookieManager $cookieManager;
    protected XBogusGenerator $xbogusGenerator;
    protected LoggerInterface $logger;

    /**
     * 支持的URL模式
     */
    protected array $supportedPatterns = [];

    /**
     * 默认请求头
     */
    protected array $defaultHeaders = [
        'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
        'Accept-Language' => 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
    ];

    public function __construct(?LoggerInterface $logger = null)
    {
        $this->connector = new DirectConnector();
        $this->cookieManager = new CookieManager();
        $this->xbogusGenerator = new XBogusGenerator();
        $this->logger = $logger ?? new NullLogger();
    }

    /**
     * 检查URL是否属于该平台
     */
    public function supportsUrl(string $url): bool
    {
        foreach ($this->supportedPatterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否正在直播
     */
    public function isLive(array $data): bool
    {
        $status = $data['status'] ?? 4;
        return $status === 2; // 2表示正在直播
    }

    /**
     * 获取主播名称
     */
    public function getAnchorName(array $data): string
    {
        return $data['anchor_name'] ?? '未知主播';
    }

    /**
     * 获取直播标题
     */
    public function getTitle(array $data): string
    {
        return $data['title'] ?? '未知标题';
    }

    /**
     * 发送HTTP请求
     * 
     * @param string $url 请求URL
     * @param array $headers 请求头
     * @param string|null $proxy 代理地址
     * @param string|null $cookies Cookie字符串
     * @return string 响应内容
     * @throws PlatformException
     */
    protected function makeRequest(string $url, array $headers = [], ?string $proxy = null, ?string $cookies = null): string
    {
        try {
            $requestHeaders = array_merge($this->defaultHeaders, $headers);

            if ($cookies) {
                $this->cookieManager->setCookies($cookies);
                $requestHeaders['Cookie'] = $this->cookieManager->getCookieString();
            }

            $this->logger->debug('Making HTTP request', [
                'url' => $url,
                'headers' => $requestHeaders,
                'proxy' => $proxy
            ]);

            $request = new RawGetRequest($url, $requestHeaders, $proxy);
            $response = $this->connector->send($request);
            $body = (string)$response->body();

            $this->logger->debug('HTTP response received', [
                'url' => $url,
                'length' => strlen($body)
            ]);

            return $body;
        } catch (\Exception $e) {
            $this->logger->error('HTTP request failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            throw new PlatformException("Failed to fetch data from {$url}: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 重试机制
     * 
     * @param callable $callback 要重试的回调函数
     * @param int $maxRetries 最大重试次数
     * @param int $delay 重试延迟(毫秒)
     * @return mixed
     * @throws PlatformException
     */
    protected function retry(callable $callback, int $maxRetries = 3, int $delay = 1000): mixed
    {
        $attempts = 0;
        $lastException = null;

        while ($attempts < $maxRetries) {
            try {
                return $callback();
            } catch (\Exception $e) {
                $lastException = $e;
                $attempts++;

                $this->logger->warning("Attempt {$attempts} failed", [
                    'error' => $e->getMessage(),
                    'attempts' => $attempts,
                    'max_retries' => $maxRetries
                ]);

                if ($attempts >= $maxRetries) {
                    break;
                }

                usleep($delay * 1000); // 转换为微秒
            }
        }

        throw new PlatformException("All {$maxRetries} attempts failed", 0, $lastException);
    }

    /**
     * 生成X-Bogus签名
     */
    protected function generateXBogus(string $url, string $userAgent): string
    {
        return $this->xbogusGenerator->generate($url, $userAgent);
    }

    /**
     * 验证流地址有效性
     */
    protected function validateStreamUrl(string $url, ?string $proxy = null): bool
    {
        try {
            $headers = ['Range' => 'bytes=0-1023']; // 只请求前1KB
            $request = new RawHeadRequest($url, $headers, $proxy);
            $response = $this->connector->send($request);
            $status = $response->status();
            return $status === 200 || $status === 206;
        } catch (\Exception $e) {
            $this->logger->warning('Stream URL validation failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
