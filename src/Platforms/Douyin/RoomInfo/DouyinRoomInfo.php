<?php

declare(strict_types=1);

namespace LiveStream\Platforms\Douyin\RoomInfo;

use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\Enum\Quality;
use LiveStream\Exceptions\PlatformException;
use Saloon\Http\Response;

class DouyinRoomInfo implements RoomInfoInterface
{
    public function __construct(private array $data) {}

    public function isLive(): bool
    {
        return (int)($this->data['status'] ?? 4) === 2;
    }

    public function getStreamUrls(string $quality = '原画', ?string $proxy = null): array
    {
        if (!$this->isLive()) {
            return ['is_live' => false];
        }

        $stream = $this->data['stream_url'] ?? [];
        $hlsMap = array_values($stream['hls_pull_url_map'] ?? []);
        $flvMap = array_values($stream['flv_pull_url'] ?? []);

        while (count($hlsMap) < 5) $hlsMap[] = end($hlsMap) ?: '';
        while (count($flvMap) < 5) $flvMap[] = end($flvMap) ?: '';

        $index = $this->getQualityIndex($quality);
        $m3u8 = $hlsMap[$index] ?? ($hlsMap[0] ?? '');
        $flv = $flvMap[$index] ?? ($flvMap[0] ?? '');

        return [
            'is_live' => true,
            'title' => $this->getTitle(),
            'quality' => $quality,
            'm3u8_url' => $m3u8,
            'flv_url' => $flv,
            'record_url' => $m3u8 ?: $flv,
        ];
    }

    public function getQualityMapping(): array
    {
        return [
            '原画' => 'ORIGIN',
            '超清' => 'UHD',
            '高清' => 'HD',
            '标清' => 'SD',
            '流畅' => 'LD',
        ];
    }

    public function supportsQuality(): bool
    {
        return true;
    }

    public function getAnchorName(): string
    {
        return (string)($this->data['anchor_name'] ?? '');
    }

    public function getTitle(): string
    {
        return (string)($this->data['title'] ?? '');
    }

    public function getRoomId(): string
    {
        return (string)($this->data['id_str'] ?? $this->data['id'] ?? '');
    }

    public function getQuality(): Quality
    {
        return Quality::ORIGINAL;
    }

    private function getQualityIndex(string $quality): int
    {
        $map = [
            '原画' => 0,
            '超清' => 1,
            '高清' => 2,
            '标清' => 3,
            '流畅' => 4,
        ];
        return $map[$quality] ?? 0;
    }
}
