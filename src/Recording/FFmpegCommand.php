<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Config\RecordingOptions;
use LiveStream\Config\StreamConfig;
use LiveStream\Enum\OutputFormat;

/**
 * FFmpeg 命令构建器
 * 
 * 专注于构建 FFmpeg 命令，不依赖具体的录制器实现
 */
final class FFmpegCommand
{
    // 常量定义，避免魔法数字
    private const DEFAULT_TIMEOUT = 15000000;
    private const DEFAULT_THREAD_QUEUE_SIZE = 1024;
    private const DEFAULT_ANALYZE_DURATION = 20000000;
    private const DEFAULT_PROBE_SIZE = 10000000;
    private const DEFAULT_BUFFER_SIZE = '8000k';
    private const DEFAULT_MAX_MUXING_QUEUE_SIZE = 1024;
    private const OVERSEAS_TIMEOUT = 50000000;
    private const OVERSEAS_ANALYZE_DURATION = 40000000;
    private const OVERSEAS_PROBE_SIZE = 20000000;
    private const OVERSEAS_BUFFER_SIZE = '15000k';
    private const OVERSEAS_MAX_MUXING_QUEUE_SIZE = 2048;

    public function __construct(
        private readonly bool $enableOverseasOptimization = false
    ) {}

    /**
     * 构建 FFmpeg 命令
     */
    public function build(StreamConfig $streamConfig, RecordingOptions $options, string $outputPath): array
    {
        $command = $this->buildBaseCommand();
        $command = array_merge($command, $this->buildInputOptions($streamConfig, $options));
        $command = array_merge($command, $this->buildOutputOptions($options, $outputPath));

        return $command;
    }

    /**
     * 构建基础命令
     */
    private function buildBaseCommand(): array
    {
        return [
            'ffmpeg',
            '-y',
            '-v',
            'verbose',
            '-loglevel',
            'error',
            '-hide_banner',
        ];
    }

    /**
     * 构建输入选项
     */
    private function buildInputOptions(StreamConfig $streamConfig, RecordingOptions $options): array
    {
        $inputOptions = [
            '-rw_timeout',
            (string)($this->enableOverseasOptimization ? self::OVERSEAS_TIMEOUT : self::DEFAULT_TIMEOUT),
            '-user_agent',
            $this->getUserAgent($streamConfig),
            '-protocol_whitelist',
            'rtmp,crypto,file,http,https,tcp,tls,udp,rtp,httpproxy',
            '-thread_queue_size',
            (string)self::DEFAULT_THREAD_QUEUE_SIZE,
            '-analyzeduration',
            (string)($this->enableOverseasOptimization ? self::OVERSEAS_ANALYZE_DURATION : self::DEFAULT_ANALYZE_DURATION),
            '-probesize',
            (string)($this->enableOverseasOptimization ? self::OVERSEAS_PROBE_SIZE : self::DEFAULT_PROBE_SIZE),
            '-fflags',
            '+discardcorrupt',
            '-re',
        ];

        // 添加代理
        if ($options->proxy !== null) {
            array_unshift($inputOptions, '-http_proxy', $options->proxy);
        }

        // 添加请求头
        $headers = $streamConfig->getAllHeaders();
        if (!empty($headers)) {
            $headerString = $this->buildHeaderString($headers);
            $inputOptions[] = '-headers';
            $inputOptions[] = $headerString;
        }

        // 添加输入 URL
        $inputOptions[] = '-i';
        $inputOptions[] = $streamConfig->getRecordUrl();

        return $inputOptions;
    }

    /**
     * 构建输出选项
     */
    private function buildOutputOptions(RecordingOptions $options, string $outputPath): array
    {
        $outputOptions = [
            '-bufsize',
            $this->enableOverseasOptimization ? self::OVERSEAS_BUFFER_SIZE : self::DEFAULT_BUFFER_SIZE,
            '-sn',
            '-dn',
            '-reconnect_delay_max',
            '60',
            '-reconnect_streamed',
            '-reconnect_at_eof',
            '-max_muxing_queue_size',
            (string)($this->enableOverseasOptimization ? self::OVERSEAS_MAX_MUXING_QUEUE_SIZE : self::DEFAULT_MAX_MUXING_QUEUE_SIZE),
            '-correct_ts_overflow',
            '1',
            '-avoid_negative_ts',
            '1',
        ];

        // 添加格式特定选项
        $outputOptions = array_merge($outputOptions, $this->buildFormatOptions($options));

        // 添加自定义 FFmpeg 选项
        $outputOptions = array_merge($outputOptions, $options->ffmpegOptions);

        // 添加输出路径
        $outputOptions[] = $outputPath;

        return $outputOptions;
    }

    /**
     * 构建格式特定选项
     */
    private function buildFormatOptions(RecordingOptions $options): array
    {
        return match ($options->format) {
            OutputFormat::MP4 => $this->buildMp4Options($options),
            OutputFormat::MKV => $this->buildMkvOptions(),
            OutputFormat::TS => $this->buildTsOptions(),
            OutputFormat::FLV => $this->buildFlvOptions(),
            OutputFormat::MP3 => $this->buildMp3Options(),
            OutputFormat::AAC => $this->buildAacOptions(),
        };
    }

    /**
     * 构建 MP4 格式选项
     */
    private function buildMp4Options(RecordingOptions $options): array
    {
        $mp4Options = [
            '-map',
            '0',
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-f',
            'mp4',
        ];

        if ($options->splitTime !== null) {
            $mp4Options = array_merge($mp4Options, [
                '-f',
                'segment',
                '-segment_time',
                (string)$options->splitTime,
                '-segment_format',
                'mp4',
                '-reset_timestamps',
                '1',
                '-movflags',
                '+frag_keyframe+empty_moov',
            ]);
        }

        return $mp4Options;
    }

    /**
     * 构建 MKV 格式选项
     */
    private function buildMkvOptions(): array
    {
        return [
            '-flags',
            'global_header',
            '-map',
            '0',
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-f',
            'matroska',
        ];
    }

    /**
     * 构建 TS 格式选项
     */
    private function buildTsOptions(): array
    {
        return [
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-f',
            'mpegts',
        ];
    }

    /**
     * 构建 FLV 格式选项（使用 TS 格式）
     */
    private function buildFlvOptions(): array
    {
        return $this->buildTsOptions();
    }

    /**
     * 构建 MP3 格式选项
     */
    private function buildMp3Options(): array
    {
        return [
            '-map',
            '0:a',
            '-c:a',
            'libmp3lame',
            '-ab',
            '320k',
        ];
    }

    /**
     * 构建 AAC 格式选项
     */
    private function buildAacOptions(): array
    {
        return [
            '-map',
            '0:a',
            '-c:a',
            'aac',
            '-bsf:a',
            'aac_adtstoasc',
            '-ab',
            '320k',
            '-movflags',
            '+faststart',
        ];
    }

    /**
     * 获取用户代理
     */
    private function getUserAgent(StreamConfig $streamConfig): string
    {
        return $streamConfig->userAgent
            ?? 'Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36';
    }

    /**
     * 构建请求头字符串
     */
    private function buildHeaderString(array $headers): string
    {
        $headerPairs = [];
        foreach ($headers as $key => $value) {
            $headerPairs[] = $key . ': ' . $value;
        }
        return implode('\r\n', $headerPairs);
    }

    /**
     * 验证 FFmpeg 是否可用
     */
    public static function validateFFmpeg(): array
    {
        $errors = [];

        // 检查 FFmpeg 是否存在
        $output = shell_exec('ffmpeg -version 2>&1');
        if ($output === null || !str_contains($output, 'ffmpeg version')) {
            $errors[] = 'FFmpeg is not installed or not accessible';
        }

        return $errors;
    }
}
