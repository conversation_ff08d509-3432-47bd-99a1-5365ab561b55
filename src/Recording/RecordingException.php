<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use Exception;

/**
 * 录制异常类
 */
class RecordingException extends Exception
{
    private ?string $recordId;
    private ?string $url;

    public function __construct(
        string $message = "",
        int $code = 0,
        ?Exception $previous = null,
        ?string $recordId = null,
        ?string $url = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->recordId = $recordId;
        $this->url = $url;
    }

    public function getRecordId(): ?string
    {
        return $this->recordId;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public static function ffmpegNotFound(): static
    {
        return new static('FFmpeg not found in system PATH');
    }

    public static function processStartFailed(string $recordId, string $url): static
    {
        return new static(
            'Failed to start recording process',
            0,
            null,
            $recordId,
            $url
        );
    }

    public static function invalidStreamUrl(string $url): static
    {
        return new static(
            'Invalid or unreachable stream URL',
            0,
            null,
            null,
            $url
        );
    }

    public static function diskSpaceInsufficient(string $path): static
    {
        return new static("Insufficient disk space at path: $path");
    }
}
