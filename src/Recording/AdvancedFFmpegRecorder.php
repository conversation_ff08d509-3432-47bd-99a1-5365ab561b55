<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use FFMpeg\FFMpeg;
use FFMpeg\FFProbe;
use FFMpeg\Format\Video\MP4;
use FFMpeg\Format\Video\WebM;
use FFMpeg\Format\Audio\Mp3;
use FFMpeg\Format\Audio\Aac;
use FFMpeg\Coordinate\Dimension;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\Exception\RuntimeException;
use LiveStream\Contracts\RecorderInterface;
use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\LiveStream;
use LiveStream\Recording\RecordingException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Process\Process;

/**
 * 基于 php-ffmpeg 的高级录制器
 * 
 * 对应 DouyinLiveRecorder main.py 的完整录制功能
 * 使用 php-ffmpeg 提供更灵活的 FFmpeg 操作
 */
class AdvancedFFmpegRecorder implements RecorderInterface
{
    private FFMpeg $ffmpeg;
    private FFProbe $ffprobe;
    private array $activeRecords = [];
    private array $recordingProcesses = [];
    private string $defaultSavePath;
    private array $defaultOptions;

    public function __construct(
        private LiveStream $liveStream,
        private LoggerInterface $logger,
        string $defaultSavePath = './recordings',
        ?array $ffmpegConfig = null
    ) {
        $this->defaultSavePath = $defaultSavePath;
        $this->initializeFFmpeg($ffmpegConfig);
        $this->setupDefaultOptions();
    }

    /**
     * 初始化 FFmpeg 实例 (对应 Python 的 FFmpeg 配置)
     */
    private function initializeFFmpeg(?array $config = null): void
    {
        $defaultConfig = [
            'ffmpeg.binaries' => $this->findFFmpegBinary(),
            'ffprobe.binaries' => $this->findFFprobeBinary(),
            'timeout' => 3600, // 1小时超时
            'ffmpeg.threads' => 12, // 多线程处理
        ];

        $config = array_merge($defaultConfig, $config ?? []);

        try {
            $this->ffmpeg = FFMpeg::create($config, $this->logger);
            $this->ffprobe = FFProbe::create($config, $this->logger);

            $this->logger->info('FFmpeg initialized successfully', [
                'ffmpeg_version' => $this->getFFmpegVersion(),
                'config' => array_filter($config, fn($k) => !str_contains($k, 'binaries'), ARRAY_FILTER_USE_KEY),
            ]);
        } catch (\Exception $e) {
            throw new RecordingException("Failed to initialize FFmpeg: " . $e->getMessage());
        }
    }

    /**
     * 设置默认选项 (对应 Python 的默认配置)
     */
    private function setupDefaultOptions(): void
    {
        $this->defaultOptions = [
            // 录制质量和格式
            'quality' => '原画',
            'format' => 'mp4',
            'video_codec' => 'copy', // 直接复制，不重新编码
            'audio_codec' => 'copy',
            'video_bitrate' => null, // 保持原始码率
            'audio_bitrate' => '320k',

            // 分辨率设置
            'resolution' => null, // 保持原始分辨率
            'max_width' => 1920,
            'max_height' => 1080,

            // 分段录制
            'split_time' => null, // 不分段
            'segment_format' => 'mp4',
            'segment_pattern' => '%03d',

            // 网络设置 (对应 Python 的网络配置)
            'proxy' => null,
            'user_agent' => 'Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36',
            'headers' => [],
            'timeout' => 3600,
            'reconnect_attempts' => 10,
            'reconnect_delay' => 5,

            // 缓冲和性能 (对应 Python 第1090-1102行)
            'buffer_size' => '8000k',
            'analyze_duration' => '20000000',
            'probe_size' => '10000000',
            'max_muxing_queue_size' => '1024',

            // 文件和路径
            'filename_template' => '{anchor_name}_{title}_{datetime}',
            'create_folder_by_platform' => true,
            'create_folder_by_author' => false,
            'create_folder_by_date' => false,
            'enable_https' => true,
        ];
    }

    /**
     * 开始录制 (对应 Python start_record 函数)
     */
    public function startRecord(string $url, array $options = []): bool
    {
        try {
            $options = array_merge($this->defaultOptions, $options);
            $recordId = $this->generateRecordId($url);

            if (isset($this->activeRecords[$recordId])) {
                $this->logger->warning("Record already exists", ['url' => $url, 'record_id' => $recordId]);
                return false;
            }

            // 步骤1：获取直播间信息
            $this->logger->info("Getting live stream info", ['url' => $url]);
            $platform = $this->liveStream->driver($url);
            $roomInfo = $platform->getRoomInfo();

            if (!$roomInfo->isLive()) {
                $this->logger->info("Stream is not live", [
                    'url' => $url,
                    'anchor_name' => $roomInfo->getAnchorName()
                ]);
                return false;
            }

            // 步骤2：获取流地址
            $streamUrls = $roomInfo->getStreamUrls($options['quality'], $options['proxy']);

            if (empty($streamUrls['record_url'])) {
                $this->logger->error("No record URL found", ['stream_urls' => $streamUrls]);
                return false;
            }

            // 步骤3：构建文件路径
            $filePath = $this->buildFilePath($roomInfo, $options);

            // 步骤4：开始 FFmpeg 录制
            $success = $this->startFFmpegRecording(
                $streamUrls['record_url'],
                $filePath,
                $options,
                $platform->getPlatformName(),
                $url,
                $recordId
            );

            if ($success) {
                // 记录录制信息
                $this->activeRecords[$recordId] = [
                    'url' => $url,
                    'platform' => $platform->getPlatformName(),
                    'anchor_name' => $roomInfo->getAnchorName(),
                    'title' => $roomInfo->getTitle(),
                    'quality' => $options['quality'],
                    'file_path' => $filePath,
                    'start_time' => time(),
                    'options' => $options,
                    'stream_info' => $streamUrls,
                ];

                $this->logger->info("Recording started successfully", [
                    'record_id' => $recordId,
                    'anchor_name' => $roomInfo->getAnchorName(),
                    'file_path' => $filePath,
                ]);
            }

            return $success;
        } catch (\Throwable $e) {
            $this->logger->error("Failed to start recording", [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * 使用 php-ffmpeg 启动录制 (核心录制逻辑)
     */
    private function startFFmpegRecording(
        string $streamUrl,
        string $filePath,
        array $options,
        string $platform,
        string $originalUrl,
        string $recordId
    ): bool {
        try {
            // 检测流信息
            $this->logger->debug("Analyzing stream", ['url' => $streamUrl]);

            // 创建 FFmpeg 命令
            $ffmpegCommand = $this->buildAdvancedFFmpegCommand($streamUrl, $filePath, $options, $platform, $originalUrl);

            // 启动异步录制进程
            $process = $this->startAsyncRecording($ffmpegCommand, $recordId);

            if ($process) {
                $this->recordingProcesses[$recordId] = $process;
                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->logger->error("FFmpeg recording failed", [
                'record_id' => $recordId,
                'error' => $e->getMessage(),
                'stream_url' => $streamUrl,
            ]);
            return false;
        }
    }

    /**
     * 构建高级 FFmpeg 命令 (对应 Python 第1104-1378行的命令构建)
     */
    private function buildAdvancedFFmpegCommand(
        string $streamUrl,
        string $filePath,
        array $options,
        string $platform,
        string $originalUrl
    ): array {
        $command = ['ffmpeg', '-y'];

        // 基础设置 (对应 Python 第1104-1124行)
        $command = array_merge($command, [
            '-v',
            'verbose',
            '-loglevel',
            'error',
            '-hide_banner',
        ]);

        // 代理设置
        if ($options['proxy']) {
            $command[] = '-http_proxy';
            $command[] = $options['proxy'];
        }

        // 网络和输入设置 (对应 Python 的网络优化)
        $isOverseas = $this->isOverseasPlatform($originalUrl);
        $command = array_merge($command, [
            '-rw_timeout',
            $isOverseas ? '50000000' : '15000000',
            '-user_agent',
            $options['user_agent'],
            '-protocol_whitelist',
            'rtmp,crypto,file,http,https,tcp,tls,udp,rtp,httpproxy',
            '-thread_queue_size',
            '1024',
            '-analyzeduration',
            $isOverseas ? '40000000' : $options['analyze_duration'],
            '-probesize',
            $isOverseas ? '20000000' : $options['probe_size'],
            '-fflags',
            '+discardcorrupt',
            '-re',
        ]);

        // 平台特定请求头 (对应 Python 第1126-1141行)
        $headers = $this->getPlatformHeaders($platform, parse_url($originalUrl, PHP_URL_HOST));
        if (!empty($headers)) {
            $headerString = '';
            foreach ($headers as $key => $value) {
                $headerString .= "$key: $value\r\n";
            }
            $command[] = '-headers';
            $command[] = rtrim($headerString, "\r\n");
        }

        // 输入源
        $command[] = '-i';
        $command[] = $streamUrl;

        // 输出设置 (对应 Python 的输出配置)
        $command = array_merge($command, [
            '-bufsize',
            $isOverseas ? '15000k' : $options['buffer_size'],
            '-sn',
            '-dn', // 禁用字幕和数据流
            '-reconnect_delay_max',
            '60',
            '-reconnect_streamed',
            '-reconnect_at_eof',
            '-max_muxing_queue_size',
            $isOverseas ? '2048' : $options['max_muxing_queue_size'],
            '-correct_ts_overflow',
            '1',
            '-avoid_negative_ts',
            '1',
        ]);

        // 编码设置
        $this->addEncodingOptions($command, $options);

        // 输出文件
        $command[] = $filePath;

        return $command;
    }

    /**
     * 添加编码选项 (对应 Python 不同格式的编码设置)
     */
    private function addEncodingOptions(array &$command, array $options): void
    {
        switch ($options['format']) {
            case 'mp4':
                if ($options['split_time']) {
                    // 分段 MP4 (对应 Python 第1357-1367行)
                    $command = array_merge($command, [
                        '-c:v',
                        $options['video_codec'],
                        '-c:a',
                        'aac',
                        '-map',
                        '0',
                        '-f',
                        'segment',
                        '-segment_time',
                        $options['split_time'],
                        '-segment_format',
                        'mp4',
                        '-reset_timestamps',
                        '1',
                        '-movflags',
                        '+frag_keyframe+empty_moov',
                    ]);
                } else {
                    // 普通 MP4 (对应 Python 第1370-1376行)
                    $command = array_merge($command, [
                        '-map',
                        '0',
                        '-c:v',
                        $options['video_codec'],
                        '-c:a',
                        $options['audio_codec'],
                        '-f',
                        'mp4',
                    ]);
                }
                break;

            case 'ts':
                if ($options['split_time']) {
                    // 分段 TS (对应 Python 第1403-1412行)
                    $command = array_merge($command, [
                        '-c:v',
                        $options['video_codec'],
                        '-c:a',
                        $options['audio_codec'],
                        '-map',
                        '0',
                        '-f',
                        'segment',
                        '-segment_time',
                        $options['split_time'],
                        '-segment_format',
                        'mpegts',
                        '-reset_timestamps',
                        '1',
                    ]);
                } else {
                    // 普通 TS (对应 Python 第1450-1456行)
                    $command = array_merge($command, [
                        '-c:v',
                        $options['video_codec'],
                        '-c:a',
                        $options['audio_codec'],
                        '-f',
                        'mpegts',
                    ]);
                }
                break;

            case 'mkv':
                // MKV 格式 (对应 Python 第1322-1329行)
                $command = array_merge($command, [
                    '-flags',
                    'global_header',
                    '-map',
                    '0',
                    '-c:v',
                    $options['video_codec'],
                    '-c:a',
                    $options['audio_codec'],
                    '-f',
                    'matroska',
                ]);
                break;

            case 'mp3':
                // 音频格式 (对应 Python 第1206-1211行)
                $command = array_merge($command, [
                    '-map',
                    '0:a',
                    '-c:a',
                    'libmp3lame',
                    '-ab',
                    $options['audio_bitrate'],
                ]);
                break;

            default:
                // 默认使用 MP4
                $command = array_merge($command, [
                    '-map',
                    '0',
                    '-c:v',
                    $options['video_codec'],
                    '-c:a',
                    $options['audio_codec'],
                    '-f',
                    'mp4',
                ]);
        }
    }

    /**
     * 启动异步录制进程 (对应 Python check_subprocess 函数)
     */
    private function startAsyncRecording(array $command, string $recordId): ?Process
    {
        try {
            $this->logger->debug("Starting recording process", [
                'record_id' => $recordId,
                'command' => implode(' ', array_slice($command, 0, 10)) . '...',
            ]);

            $process = new Process($command);
            $process->setTimeout(null); // 无超时限制
            $process->start();

            // 监控进程输出
            $this->monitorRecordingProcess($process, $recordId);

            return $process;
        } catch (\Exception $e) {
            $this->logger->error("Failed to start recording process", [
                'record_id' => $recordId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * 监控录制进程 (对应 Python 的进程监控逻辑)
     */
    private function monitorRecordingProcess(Process $process, string $recordId): void
    {
        // 异步监控进程状态
        $process->wait(function ($type, $buffer) use ($recordId) {
            if (Process::ERR === $type) {
                // FFmpeg 错误输出
                if (str_contains($buffer, 'error') || str_contains($buffer, 'failed')) {
                    $this->logger->error("FFmpeg error", [
                        'record_id' => $recordId,
                        'output' => trim($buffer),
                    ]);
                }
            } else {
                // FFmpeg 正常输出
                if (str_contains($buffer, 'frame=') || str_contains($buffer, 'time=')) {
                    $this->logger->debug("Recording progress", [
                        'record_id' => $recordId,
                        'progress' => trim($buffer),
                    ]);
                }
            }
        });

        // 检查进程结束状态
        $exitCode = $process->getExitCode();
        if ($exitCode === 0) {
            $this->logger->info("Recording completed successfully", [
                'record_id' => $recordId,
                'exit_code' => $exitCode,
            ]);
        } else {
            $this->logger->error("Recording failed", [
                'record_id' => $recordId,
                'exit_code' => $exitCode,
                'error_output' => $process->getErrorOutput(),
            ]);
        }
    }

    /**
     * 停止录制
     */
    public function stopRecord(string $recordId): bool
    {
        if (!isset($this->activeRecords[$recordId])) {
            return false;
        }

        try {
            $process = $this->recordingProcesses[$recordId] ?? null;
            if ($process && $process->isRunning()) {
                // 优雅停止
                $process->signal(SIGTERM);

                // 等待进程结束
                for ($i = 0; $i < 10; $i++) {
                    if (!$process->isRunning()) {
                        break;
                    }
                    sleep(1);
                }

                // 强制终止
                if ($process->isRunning()) {
                    $process->stop();
                }
            }

            $recordInfo = $this->activeRecords[$recordId];
            $this->logger->info("Recording stopped", [
                'record_id' => $recordId,
                'anchor_name' => $recordInfo['anchor_name'],
                'duration' => time() - $recordInfo['start_time'],
            ]);

            unset($this->activeRecords[$recordId]);
            unset($this->recordingProcesses[$recordId]);

            return true;
        } catch (\Throwable $e) {
            $this->logger->error("Failed to stop recording", [
                'record_id' => $recordId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 获取录制状态
     */
    public function getRecordStatus(string $recordId): array
    {
        if (!isset($this->activeRecords[$recordId])) {
            return ['status' => 'not_found'];
        }

        $process = $this->recordingProcesses[$recordId] ?? null;
        $recordInfo = $this->activeRecords[$recordId];

        $status = [
            'status' => $process && $process->isRunning() ? 'recording' : 'stopped',
            'record_id' => $recordId,
            'platform' => $recordInfo['platform'],
            'anchor_name' => $recordInfo['anchor_name'],
            'title' => $recordInfo['title'],
            'file_path' => $recordInfo['file_path'],
            'start_time' => $recordInfo['start_time'],
            'duration' => time() - $recordInfo['start_time'],
            'quality' => $recordInfo['quality'],
        ];

        // 添加文件信息
        if (file_exists($recordInfo['file_path'])) {
            $status['file_size'] = filesize($recordInfo['file_path']);
            $status['file_size_mb'] = round($status['file_size'] / 1024 / 1024, 2);
        }

        return $status;
    }

    /**
     * 获取所有活跃录制
     */
    public function getActiveRecords(): array
    {
        $activeRecords = [];

        foreach ($this->activeRecords as $recordId => $recordInfo) {
            $activeRecords[$recordId] = $this->getRecordStatus($recordId);
        }

        return $activeRecords;
    }

    // =========================== 辅助方法 ===========================

    private function generateRecordId(string $url): string
    {
        return md5($url . time());
    }

    private function buildFilePath(RoomInfoInterface $roomInfo, array $options): string
    {
        $basePath = $options['save_path'] ?? $this->defaultSavePath;
        $platform = '抖音直播'; // 可以根据实际情况确定

        $anchorName = $this->cleanFileName($roomInfo->getAnchorName());
        $title = $this->cleanFileName($roomInfo->getTitle());

        $fullPath = $basePath;

        if ($options['create_folder_by_platform']) {
            $fullPath .= DIRECTORY_SEPARATOR . $platform;
        }

        if ($options['create_folder_by_author']) {
            $fullPath .= DIRECTORY_SEPARATOR . $anchorName;
        }

        if ($options['create_folder_by_date']) {
            $fullPath .= DIRECTORY_SEPARATOR . date('Y-m-d');
        }

        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        $datetime = date('Y-m-d_H-i-s');
        $extension = $this->getFileExtension($options['format']);

        $filename = str_replace([
            '{anchor_name}',
            '{title}',
            '{datetime}',
            '{quality}',
        ], [
            $anchorName,
            $title,
            $datetime,
            $options['quality'],
        ], $options['filename_template']);

        $filename = $this->cleanFileName($filename);

        if ($options['split_time']) {
            $filename .= '_' . $options['segment_pattern'];
        }

        $filename .= '.' . $extension;

        return $fullPath . DIRECTORY_SEPARATOR . $filename;
    }

    private function cleanFileName(string $filename): string
    {
        if (empty($filename)) {
            return 'unnamed';
        }

        $cleaned = preg_replace('/[<>:"\\/\\\\|?*]/', '_', $filename);
        $cleaned = preg_replace('/[\x00-\x1f\x7f]/', '', $cleaned);
        $cleaned = trim($cleaned, '. ');

        return $cleaned ?: 'unnamed';
    }

    private function getFileExtension(string $format): string
    {
        return match ($format) {
            'mp4' => 'mp4',
            'mkv' => 'mkv',
            'ts' => 'ts',
            'mp3' => 'mp3',
            'aac' => 'aac',
            default => 'mp4',
        };
    }

    private function findFFmpegBinary(): string
    {
        $paths = ['/usr/bin/ffmpeg', '/usr/local/bin/ffmpeg', 'ffmpeg'];

        foreach ($paths as $path) {
            if (is_executable($path)) {
                return $path;
            }
        }

        // 尝试通过 which 命令查找
        $result = shell_exec('which ffmpeg 2>/dev/null');
        if ($result && is_executable(trim($result))) {
            return trim($result);
        }

        throw new RecordingException("FFmpeg binary not found");
    }

    private function findFFprobeBinary(): string
    {
        $paths = ['/usr/bin/ffprobe', '/usr/local/bin/ffprobe', 'ffprobe'];

        foreach ($paths as $path) {
            if (is_executable($path)) {
                return $path;
            }
        }

        $result = shell_exec('which ffprobe 2>/dev/null');
        if ($result && is_executable(trim($result))) {
            return trim($result);
        }

        throw new RecordingException("FFprobe binary not found");
    }

    private function getFFmpegVersion(): string
    {
        try {
            $result = shell_exec('ffmpeg -version 2>&1');
            if ($result && preg_match('/ffmpeg version ([^\s]+)/', $result, $matches)) {
                return $matches[1];
            }
        } catch (\Exception $e) {
            // 忽略错误
        }

        return 'unknown';
    }

    private function isOverseasPlatform(string $url): bool
    {
        $overseasHosts = [
            'tiktok.com',
            'twitch.tv',
            'youtube.com',
            'popkontv.com',
            'flextv.co.kr',
            'winktv.co.kr',
            'pandalive.co.kr',
            'liveme.com'
        ];

        foreach ($overseasHosts as $host) {
            if (str_contains($url, $host)) {
                return true;
            }
        }

        return false;
    }

    private function getPlatformHeaders(string $platform, string $host): array
    {
        $headers = [
            'shopee' => ['origin' => "https://$host"],
            '17Live' => ['referer' => 'https://17.live/en/live/6302408'],
            '浪Live' => ['referer' => 'https://www.lang.live'],
            'Blued直播' => ['referer' => 'https://app.blued.cn'],
        ];

        return $headers[$platform] ?? [];
    }
}
