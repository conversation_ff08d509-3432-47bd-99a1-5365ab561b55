<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Contracts\RecorderInterface;
use LiveStream\Contracts\RecordingResultInterface;
use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\Config\RecordingOptions;
use LiveStream\Config\StreamConfig;
use LiveStream\Enum\OutputFormat;
use LiveStream\Utils\PathBuilder;

/**
 * 简化的流录制器
 * 
 * 专注于构建录制命令，不包含进程管理逻辑
 * 客户端代码负责执行命令和进程管理
 */
final class StreamRecorder implements RecorderInterface
{
    private const SUPPORTED_FORMATS = [
        'mp4',
        'mkv',
        'ts',
        'flv',
        'mp3',
        'aac'
    ];

    public function __construct(
        private readonly PathBuilder $pathBuilder = new PathBuilder(),
        private readonly bool $enableOverseasOptimization = false
    ) {}

    public function record(RoomInfoInterface $roomInfo, RecordingOptions $options): RecordingResultInterface
    {
        // 验证配置
        $configErrors = $options->validate();
        if (!empty($configErrors)) {
            return RecordingResult::failure(
                'Invalid configuration: ' . implode(', ', $configErrors),
                $options,
                StreamConfig::fromArray([]),
                $roomInfo
            );
        }

        // 检查直播状态
        if (!$roomInfo->isLive()) {
            return RecordingResult::failure(
                'Stream is not live',
                $options,
                StreamConfig::fromArray([]),
                $roomInfo
            );
        }

        // 获取流地址
        $streamUrls = $roomInfo->getStreamUrls($options->quality->getDisplayName(), $options->proxy);

        if (empty($streamUrls['record_url'])) {
            return RecordingResult::failure(
                'No valid stream URL found',
                $options,
                StreamConfig::fromArray($streamUrls),
                $roomInfo
            );
        }

        // 创建流配置
        $streamConfig = StreamConfig::fromArray([
            'url' => $streamUrls['record_url'],
            'record_url' => $streamUrls['record_url'],
            'm3u8_url' => $streamUrls['m3u8_url'] ?? null,
            'flv_url' => $streamUrls['flv_url'] ?? null,
        ]);

        // 构建输出路径
        $outputPath = $this->pathBuilder->buildFilePath($roomInfo, $options);

        try {
            // 构建 FFmpeg 命令
            $command = $this->buildCommand($streamConfig, $options, $outputPath);

            return RecordingResult::success(
                $outputPath,
                $command,
                $options,
                $streamConfig,
                $roomInfo,
                [
                    'created_at' => time(),
                    'quality' => $options->quality->getDisplayName(),
                    'format' => $options->format->value,
                ]
            );
        } catch (\Throwable $e) {
            return RecordingResult::failure(
                'Failed to build recording command: ' . $e->getMessage(),
                $options,
                $streamConfig,
                $roomInfo
            );
        }
    }

    public function buildCommand(StreamConfig $streamConfig, RecordingOptions $options, string $outputPath): array
    {
        $commandBuilder = new FFmpegCommand($this->enableOverseasOptimization);
        return $commandBuilder->build($streamConfig, $options, $outputPath);
    }

    public function validateEnvironment(): array
    {
        $errors = [];

        // 验证 FFmpeg
        $ffmpegErrors = FFmpegCommand::validateFFmpeg();
        $errors = array_merge($errors, $ffmpegErrors);

        return $errors;
    }

    public function getSupportedFormats(): array
    {
        return self::SUPPORTED_FORMATS;
    }

    /**
     * 检查是否为海外平台
     */
    public static function isOverseasPlatform(string $url): bool
    {
        $overseasHosts = [
            'tiktok.com',
            'twitch.tv',
            'youtube.com',
            'popkontv.com',
            'flextv.co.kr',
            'winktv.co.kr',
            'pandalive.co.kr',
            'shopee',
            'liveme.com'
        ];

        foreach ($overseasHosts as $host) {
            if (str_contains($url, $host)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 创建支持海外平台的录制器实例
     */
    public static function forOverseas(): self
    {
        return new self(new PathBuilder(), true);
    }
}
