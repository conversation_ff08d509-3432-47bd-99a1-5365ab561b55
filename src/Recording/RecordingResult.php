<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Contracts\RecordingResultInterface;
use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\Config\RecordingOptions;
use LiveStream\Config\StreamConfig;

/**
 * 录制结果实现
 */
final readonly class RecordingR<PERSON>ult implements RecordingResultInterface
{
    public function __construct(
        private bool $successful,
        private string $outputPath,
        private array $command,
        private RecordingOptions $options,
        private StreamConfig $streamConfig,
        private RoomInfoInterface $roomInfo,
        private ?string $error = null,
        private array $metadata = [],
    ) {}

    public function isSuccessful(): bool
    {
        return $this->successful;
    }

    public function getOutputPath(): string
    {
        return $this->outputPath;
    }

    public function getCommand(): array
    {
        return $this->command;
    }

    public function getOptions(): RecordingOptions
    {
        return $this->options;
    }

    public function getStreamConfig(): StreamConfig
    {
        return $this->streamConfig;
    }

    public function getRoomInfo(): RoomInfoInterface
    {
        return $this->roomInfo;
    }

    public function getError(): ?string
    {
        return $this->error;
    }

    public function getMetadata(): array
    {
        return $this->metadata;
    }

    public function toArray(): array
    {
        return [
            'successful' => $this->successful,
            'output_path' => $this->outputPath,
            'command' => $this->command,
            'options' => $this->options->toArray(),
            'stream_config' => $this->streamConfig->toArray(),
            'room_info' => [
                'anchor_name' => $this->roomInfo->getAnchorName(),
                'title' => $this->roomInfo->getTitle(),
                'room_id' => $this->roomInfo->getRoomId(),
                'is_live' => $this->roomInfo->isLive(),
            ],
            'error' => $this->error,
            'metadata' => $this->metadata,
        ];
    }

    /**
     * 创建成功的录制结果
     */
    public static function success(
        string $outputPath,
        array $command,
        RecordingOptions $options,
        StreamConfig $streamConfig,
        RoomInfoInterface $roomInfo,
        array $metadata = []
    ): self {
        return new self(
            successful: true,
            outputPath: $outputPath,
            command: $command,
            options: $options,
            streamConfig: $streamConfig,
            roomInfo: $roomInfo,
            metadata: $metadata
        );
    }

    /**
     * 创建失败的录制结果
     */
    public static function failure(
        string $error,
        RecordingOptions $options,
        StreamConfig $streamConfig,
        RoomInfoInterface $roomInfo,
        array $command = [],
        string $outputPath = '',
        array $metadata = []
    ): self {
        return new self(
            successful: false,
            outputPath: $outputPath,
            command: $command,
            options: $options,
            streamConfig: $streamConfig,
            roomInfo: $roomInfo,
            error: $error,
            metadata: $metadata
        );
    }
}
