<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Contracts\RecorderInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Process\Process;

/**
 * 录制管理器
 * 
 * 负责管理多个录制任务，提供批量操作和监控功能
 * 对应 Python main.py 中的全局录制管理逻辑
 */
class RecordingManager
{
    private array $recorders = [];
    private array $scheduledTasks = [];
    private bool $isRunning = false;

    public function __construct(
        private RecorderInterface $defaultRecorder,
        private LoggerInterface $logger
    ) {}

    /**
     * 添加录制任务
     */
    public function addRecordingTask(string $url, array $options = []): string
    {
        $taskId = uniqid('task_');

        $this->scheduledTasks[$taskId] = [
            'url' => $url,
            'options' => $options,
            'created_at' => time(),
            'status' => 'scheduled',
            'retry_count' => 0,
            'max_retries' => $options['max_retries'] ?? 3,
        ];

        $this->logger->info("Recording task added", [
            'task_id' => $taskId,
            'url' => $url,
        ]);

        return $taskId;
    }

    /**
     * 批量添加录制任务
     */
    public function addBatchRecordingTasks(array $urlList): array
    {
        $taskIds = [];

        foreach ($urlList as $item) {
            if (is_string($item)) {
                $taskIds[] = $this->addRecordingTask($item);
            } elseif (is_array($item) && isset($item['url'])) {
                $taskIds[] = $this->addRecordingTask($item['url'], $item['options'] ?? []);
            }
        }

        $this->logger->info("Batch recording tasks added", [
            'count' => count($taskIds),
            'task_ids' => $taskIds,
        ]);

        return $taskIds;
    }

    /**
     * 启动录制管理器 (对应 Python 主循环逻辑)
     */
    public function start(): void
    {
        $this->isRunning = true;
        $this->logger->info("Recording manager started");

        while ($this->isRunning) {
            try {
                $this->processScheduledTasks();
                $this->monitorActiveRecords();
                $this->cleanupCompletedTasks();

                // 短暂休眠，避免过度占用CPU
                sleep(5);
            } catch (\Throwable $e) {
                $this->logger->error("Error in recording manager loop", [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
                sleep(10); // 出错时等待更长时间
            }
        }

        $this->logger->info("Recording manager stopped");
    }

    /**
     * 停止录制管理器
     */
    public function stop(): void
    {
        $this->isRunning = false;

        // 停止所有活跃的录制
        $activeRecords = $this->defaultRecorder->getActiveRecords();
        foreach ($activeRecords as $recordId => $recordInfo) {
            $this->defaultRecorder->stopRecord($recordId);
        }

        $this->logger->info("Recording manager stopping", [
            'stopped_records' => count($activeRecords),
        ]);
    }

    /**
     * 处理计划中的任务
     */
    private function processScheduledTasks(): void
    {
        foreach ($this->scheduledTasks as $taskId => $task) {
            if ($task['status'] !== 'scheduled') {
                continue;
            }

            try {
                $success = $this->defaultRecorder->startRecord($task['url'], $task['options']);

                if ($success) {
                    $this->scheduledTasks[$taskId]['status'] = 'recording';
                    $this->scheduledTasks[$taskId]['started_at'] = time();

                    $this->logger->info("Recording task started", [
                        'task_id' => $taskId,
                        'url' => $task['url'],
                    ]);
                } else {
                    $this->handleTaskFailure($taskId, "Failed to start recording");
                }
            } catch (\Throwable $e) {
                $this->handleTaskFailure($taskId, $e->getMessage());
            }
        }
    }

    /**
     * 监控活跃的录制任务
     */
    private function monitorActiveRecords(): void
    {
        $activeRecords = $this->defaultRecorder->getActiveRecords();

        foreach ($activeRecords as $recordId => $recordInfo) {
            // 检查录制是否异常停止
            if ($recordInfo['status'] === 'stopped') {
                $this->logger->warning("Recording stopped unexpectedly", [
                    'record_id' => $recordId,
                    'anchor_name' => $recordInfo['anchor_name'],
                    'duration' => $recordInfo['duration'],
                ]);

                // 尝试重新启动录制
                $this->handleRecordingStopped($recordId, $recordInfo);
            }

            // 检查长时间运行的录制
            $maxDuration = 3600 * 8; // 8小时
            if ($recordInfo['duration'] > $maxDuration) {
                $this->logger->info("Long running recording detected", [
                    'record_id' => $recordId,
                    'duration' => $recordInfo['duration'],
                ]);
            }
        }
    }

    /**
     * 清理已完成的任务
     */
    private function cleanupCompletedTasks(): void
    {
        $cutoffTime = time() - 3600; // 保留1小时内的任务记录

        foreach ($this->scheduledTasks as $taskId => $task) {
            if (
                in_array($task['status'], ['completed', 'failed']) &&
                $task['created_at'] < $cutoffTime
            ) {
                unset($this->scheduledTasks[$taskId]);
            }
        }
    }

    /**
     * 处理任务失败
     */
    private function handleTaskFailure(string $taskId, string $reason): void
    {
        $task = $this->scheduledTasks[$taskId];
        $task['retry_count']++;
        $task['last_error'] = $reason;

        if ($task['retry_count'] >= $task['max_retries']) {
            $task['status'] = 'failed';
            $this->logger->error("Recording task failed permanently", [
                'task_id' => $taskId,
                'url' => $task['url'],
                'retry_count' => $task['retry_count'],
                'reason' => $reason,
            ]);
        } else {
            $task['status'] = 'scheduled'; // 重新调度
            $task['next_retry_time'] = time() + (60 * $task['retry_count']); // 指数退避

            $this->logger->warning("Recording task failed, will retry", [
                'task_id' => $taskId,
                'url' => $task['url'],
                'retry_count' => $task['retry_count'],
                'next_retry_time' => $task['next_retry_time'],
                'reason' => $reason,
            ]);
        }

        $this->scheduledTasks[$taskId] = $task;
    }

    /**
     * 处理录制意外停止
     */
    private function handleRecordingStopped(string $recordId, array $recordInfo): void
    {
        // 找到对应的任务
        foreach ($this->scheduledTasks as $taskId => $task) {
            if ($task['url'] === $recordInfo['url'] && $task['status'] === 'recording') {
                // 重新调度任务
                $this->scheduledTasks[$taskId]['status'] = 'scheduled';
                $this->scheduledTasks[$taskId]['retry_count']++;

                $this->logger->info("Rescheduling stopped recording", [
                    'task_id' => $taskId,
                    'record_id' => $recordId,
                    'url' => $recordInfo['url'],
                ]);

                break;
            }
        }
    }

    /**
     * 获取管理器状态
     */
    public function getStatus(): array
    {
        $scheduled = array_filter($this->scheduledTasks, fn($task) => $task['status'] === 'scheduled');
        $recording = array_filter($this->scheduledTasks, fn($task) => $task['status'] === 'recording');
        $failed = array_filter($this->scheduledTasks, fn($task) => $task['status'] === 'failed');
        $completed = array_filter($this->scheduledTasks, fn($task) => $task['status'] === 'completed');

        return [
            'is_running' => $this->isRunning,
            'total_tasks' => count($this->scheduledTasks),
            'scheduled_count' => count($scheduled),
            'recording_count' => count($recording),
            'failed_count' => count($failed),
            'completed_count' => count($completed),
            'active_records' => $this->defaultRecorder->getActiveRecords(),
        ];
    }

    /**
     * 获取任务详情
     */
    public function getTaskDetails(string $taskId): ?array
    {
        return $this->scheduledTasks[$taskId] ?? null;
    }

    /**
     * 取消任务
     */
    public function cancelTask(string $taskId): bool
    {
        if (!isset($this->scheduledTasks[$taskId])) {
            return false;
        }

        $task = $this->scheduledTasks[$taskId];

        // 如果正在录制，停止录制
        if ($task['status'] === 'recording') {
            $activeRecords = $this->defaultRecorder->getActiveRecords();
            foreach ($activeRecords as $recordId => $recordInfo) {
                if ($recordInfo['url'] === $task['url']) {
                    $this->defaultRecorder->stopRecord($recordId);
                    break;
                }
            }
        }

        $this->scheduledTasks[$taskId]['status'] = 'cancelled';

        $this->logger->info("Task cancelled", [
            'task_id' => $taskId,
            'url' => $task['url'],
        ]);

        return true;
    }
}
