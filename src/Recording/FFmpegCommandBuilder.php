<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use FFMpeg\FFMpeg;
use FFMpeg\Format\Video\X264;
use FFMpeg\Format\Video\WebM;
use FFMpeg\Format\Audio\Mp3;
use FFMpeg\Format\Audio\Aac;
use Psr\Log\LoggerInterface;

/**
 * FFmpeg 命令构建器
 * 
 * 基于 DouyinLiveRecorder main.py 第1104-1124行的FFmpeg命令构建逻辑
 * 增强版：支持 php-ffmpeg 和原生命令行两种模式
 */
class FFmpegCommandBuilder
{
    private array $baseCommand = [];
    private array $inputOptions = [];
    private array $outputOptions = [];
    private ?string $inputUrl = null;
    private ?string $outputPath = null;
    private ?string $proxyAddress = null;
    private array $headers = [];
    private bool $usePhpFFmpeg = false;
    private ?FFMpeg $ffmpeg = null;
    private ?LoggerInterface $logger = null;

    public function __construct(bool $usePhpFFmpeg = false, ?LoggerInterface $logger = null)
    {
        $this->usePhpFFmpeg = $usePhpFFmpeg;
        $this->logger = $logger;

        if ($usePhpFFmpeg) {
            $this->initializePhpFFmpeg();
        }

        $this->resetToDefaults();
    }

    /**
     * 初始化 php-ffmpeg
     */
    private function initializePhpFFmpeg(): void
    {
        try {
            $config = [
                'ffmpeg.binaries' => $this->findFFmpegBinary(),
                'ffprobe.binaries' => $this->findFFprobeBinary(),
                'timeout' => 3600,
                'ffmpeg.threads' => 12,
            ];

            $this->ffmpeg = FFMpeg::create($config, $this->logger);
        } catch (\Exception $e) {
            throw new RecordingException("Failed to initialize php-ffmpeg: " . $e->getMessage());
        }
    }

    /**
     * 重置为默认设置 (对应 Python 第1104-1124行)
     */
    private function resetToDefaults(): self
    {
        $this->baseCommand = [
            'ffmpeg',
            '-y',
            '-v',
            'verbose',
            '-loglevel',
            'error',
            '-hide_banner',
        ];

        $this->inputOptions = [
            '-rw_timeout',
            '15000000',
            '-user_agent',
            'Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36',
            '-protocol_whitelist',
            'rtmp,crypto,file,http,https,tcp,tls,udp,rtp,httpproxy',
            '-thread_queue_size',
            '1024',
            '-analyzeduration',
            '20000000',
            '-probesize',
            '10000000',
            '-fflags',
            '+discardcorrupt',
            '-re',
        ];

        $this->outputOptions = [
            '-bufsize',
            '8000k',
            '-sn',
            '-dn',
            '-reconnect_delay_max',
            '60',
            '-reconnect_streamed',
            '-reconnect_at_eof',
            '-max_muxing_queue_size',
            '1024',
            '-correct_ts_overflow',
            '1',
            '-avoid_negative_ts',
            '1',
        ];

        return $this;
    }

    /**
     * 设置输入URL
     */
    public function setInputUrl(string $url): self
    {
        $this->inputUrl = $url;
        return $this;
    }

    /**
     * 设置输出路径
     */
    public function setOutputPath(string $path): self
    {
        $this->outputPath = $path;
        return $this;
    }

    /**
     * 设置代理地址 (对应 Python 第1143-1145行)
     */
    public function setProxy(string $proxyAddress): self
    {
        $this->proxyAddress = $proxyAddress;
        return $this;
    }

    /**
     * 设置请求头 (对应 Python 第1138-1141行)
     */
    public function setHeaders(array $headers): self
    {
        $this->headers = $headers;
        return $this;
    }

    /**
     * 为海外平台优化参数 (对应 Python 第1095-1102行)
     */
    public function setOverseasOptimization(): self
    {
        $this->inputOptions['-rw_timeout'] = '50000000';
        $this->inputOptions['-analyzeduration'] = '40000000';
        $this->inputOptions['-probesize'] = '20000000';
        $this->outputOptions['-bufsize'] = '15000k';
        $this->outputOptions['-max_muxing_queue_size'] = '2048';
        return $this;
    }

    /**
     * 构建 MP4 输出格式 (对应 Python 第1370-1376行)
     */
    public function buildForMp4(): array
    {
        $outputOptions = [
            '-map',
            '0',
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-f',
            'mp4',
        ];

        return $this->buildCommand($outputOptions);
    }

    /**
     * 构建 MP4 分段输出格式 (对应 Python 第1357-1367行)
     */
    public function buildForMp4Segments(string $segmentTime): array
    {
        $outputOptions = [
            '-c:v',
            'copy',
            '-c:a',
            'aac',
            '-map',
            '0',
            '-f',
            'segment',
            '-segment_time',
            $segmentTime,
            '-segment_format',
            'mp4',
            '-reset_timestamps',
            '1',
            '-movflags',
            '+frag_keyframe+empty_moov',
        ];

        return $this->buildCommand($outputOptions);
    }

    /**
     * 构建 TS 输出格式 (对应 Python 第1450-1456行)
     */
    public function buildForTs(): array
    {
        $outputOptions = [
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-f',
            'mpegts',
        ];

        return $this->buildCommand($outputOptions);
    }

    /**
     * 构建 TS 分段输出格式 (对应 Python 第1403-1412行)
     */
    public function buildForTsSegments(string $segmentTime): array
    {
        $outputOptions = [
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-map',
            '0',
            '-f',
            'segment',
            '-segment_time',
            $segmentTime,
            '-segment_format',
            'mpegts',
            '-reset_timestamps',
            '1',
        ];

        return $this->buildCommand($outputOptions);
    }

    /**
     * 构建 MKV 输出格式 (对应 Python 第1322-1329行)
     */
    public function buildForMkv(): array
    {
        $outputOptions = [
            '-flags',
            'global_header',
            '-map',
            '0',
            '-c:v',
            'copy',
            '-c:a',
            'copy',
            '-f',
            'matroska',
        ];

        return $this->buildCommand($outputOptions);
    }

    /**
     * 构建音频输出格式 (对应 Python 第1206-1221行)
     */
    public function buildForAudio(string $format = 'aac'): array
    {
        if ($format === 'mp3') {
            $outputOptions = [
                '-map',
                '0:a',
                '-c:a',
                'libmp3lame',
                '-ab',
                '320k',
            ];
        } else {
            $outputOptions = [
                '-map',
                '0:a',
                '-c:a',
                'aac',
                '-bsf:a',
                'aac_adtstoasc',
                '-ab',
                '320k',
                '-movflags',
                '+faststart',
            ];
        }

        return $this->buildCommand($outputOptions);
    }

    /**
     * 构建最终的 FFmpeg 命令
     */
    private function buildCommand(array $formatOptions): array
    {
        if (!$this->inputUrl || !$this->outputPath) {
            throw new \InvalidArgumentException('Input URL and output path must be set');
        }

        $command = $this->baseCommand;

        // 添加代理设置
        if ($this->proxyAddress) {
            array_splice($command, 1, 0, ['-http_proxy', $this->proxyAddress]);
        }

        // 添加输入选项
        $command = array_merge($command, $this->inputOptions);

        // 添加请求头
        if (!empty($this->headers)) {
            $headerString = '';
            foreach ($this->headers as $key => $value) {
                $headerString .= $key . ': ' . $value . '\r\n';
            }
            $command[] = '-headers';
            $command[] = rtrim($headerString, '\r\n');
        }

        // 添加输入URL
        $command[] = '-i';
        $command[] = $this->inputUrl;

        // 添加输出选项
        $command = array_merge($command, $this->outputOptions);

        // 添加格式特定选项
        $command = array_merge($command, $formatOptions);

        // 添加输出路径
        $command[] = $this->outputPath;

        return $command;
    }

    /**
     * 获取特定平台的请求头 (对应 Python 第1126-1136行)
     */
    public static function getPlatformHeaders(string $platform, string $liveDomain = ''): array
    {
        $headers = [
            'PandaTV' => ['origin' => 'https://www.pandalive.co.kr'],
            'WinkTV' => ['origin' => 'https://www.winktv.co.kr'],
            'PopkonTV' => ['origin' => 'https://www.popkontv.com'],
            'FlexTV' => ['origin' => 'https://www.flextv.co.kr'],
            '千度热播' => ['referer' => 'https://qiandurebo.com'],
            '17Live' => ['referer' => 'https://17.live/en/live/6302408'],
            '浪Live' => ['referer' => 'https://www.lang.live'],
            'shopee' => ['origin' => $liveDomain],
            'Blued直播' => ['referer' => 'https://app.blued.cn'],
        ];

        return $headers[$platform] ?? [];
    }

    /**
     * 检查是否为海外平台 (对应 Python 第1095-1102行)
     */
    public static function isOverseasPlatform(string $url): bool
    {
        $overseasHosts = [
            'tiktok.com',
            'twitch.tv',
            'youtube.com',
            'popkontv.com',
            'flextv.co.kr',
            'winktv.co.kr',
            'pandalive.co.kr',
            'shopee',
            'liveme.com'
        ];

        foreach ($overseasHosts as $host) {
            if (str_contains($url, $host)) {
                return true;
            }
        }

        return false;
    }

    // =========================== php-ffmpeg 辅助方法 ===========================

    /**
     * 查找 FFmpeg 二进制文件
     */
    private function findFFmpegBinary(): string
    {
        $paths = ['/usr/bin/ffmpeg', '/usr/local/bin/ffmpeg', 'ffmpeg'];

        foreach ($paths as $path) {
            if (is_executable($path)) {
                return $path;
            }
        }

        $result = shell_exec('which ffmpeg 2>/dev/null');
        if ($result && is_executable(trim($result))) {
            return trim($result);
        }

        throw new RecordingException("FFmpeg binary not found");
    }

    /**
     * 查找 FFprobe 二进制文件
     */
    private function findFFprobeBinary(): string
    {
        $paths = ['/usr/bin/ffprobe', '/usr/local/bin/ffprobe', 'ffprobe'];

        foreach ($paths as $path) {
            if (is_executable($path)) {
                return $path;
            }
        }

        $result = shell_exec('which ffprobe 2>/dev/null');
        if ($result && is_executable(trim($result))) {
            return trim($result);
        }

        throw new RecordingException("FFprobe binary not found");
    }

    /**
     * 获取 php-ffmpeg 实例
     */
    public function getPhpFFmpeg(): ?FFMpeg
    {
        return $this->ffmpeg;
    }

    /**
     * 检查是否使用 php-ffmpeg
     */
    public function isUsingPhpFFmpeg(): bool
    {
        return $this->usePhpFFmpeg && $this->ffmpeg !== null;
    }

    /**
     * 创建 php-ffmpeg 格式对象
     */
    public function createPhpFFmpegFormat(string $formatName, array $options = []): mixed
    {
        if (!$this->usePhpFFmpeg) {
            throw new RecordingException("php-ffmpeg not enabled");
        }

        return match ($formatName) {
            'mp4' => $this->createMP4Format($options),
            'webm' => $this->createWebMFormat($options),
            'mp3' => $this->createMP3Format($options),
            'aac' => $this->createAACFormat($options),
            default => $this->createMP4Format($options),
        };
    }

    /**
     * 创建 MP4 格式
     */
    private function createMP4Format(array $options = []): X264
    {
        $format = new X264();

        if (isset($options['video_codec']) && $options['video_codec'] !== 'copy') {
            $format->setVideoCodec($options['video_codec']);
        }

        if (isset($options['audio_codec']) && $options['audio_codec'] !== 'copy') {
            $format->setAudioCodec($options['audio_codec']);
        }

        if (isset($options['video_bitrate'])) {
            $format->setKiloBitrate((int)$options['video_bitrate']);
        }

        if (isset($options['audio_bitrate'])) {
            $format->setAudioKiloBitrate((int)str_replace('k', '', $options['audio_bitrate']));
        }

        return $format;
    }

    /**
     * 创建 WebM 格式
     */
    private function createWebMFormat(array $options = []): WebM
    {
        $format = new WebM();

        if (isset($options['video_codec']) && $options['video_codec'] !== 'copy') {
            $format->setVideoCodec($options['video_codec']);
        }

        if (isset($options['audio_codec']) && $options['audio_codec'] !== 'copy') {
            $format->setAudioCodec($options['audio_codec']);
        }

        return $format;
    }

    /**
     * 创建 MP3 格式
     */
    private function createMP3Format(array $options = []): Mp3
    {
        $format = new Mp3();

        if (isset($options['audio_bitrate'])) {
            $format->setAudioKiloBitrate((int)str_replace('k', '', $options['audio_bitrate']));
        }

        return $format;
    }

    /**
     * 创建 AAC 格式
     */
    private function createAACFormat(array $options = []): Aac
    {
        $format = new Aac();

        if (isset($options['audio_bitrate'])) {
            $format->setAudioKiloBitrate((int)str_replace('k', '', $options['audio_bitrate']));
        }

        return $format;
    }
}
