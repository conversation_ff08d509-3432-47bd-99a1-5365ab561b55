<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use FFMpeg\FFProbe;
use FFMpeg\FFProbe\DataMapping\Stream;
use FFMpeg\FFProbe\DataMapping\Format;
use Psr\Log\LoggerInterface;

/**
 * 流分析器
 * 
 * 使用 php-ffmpeg 分析直播流的详细信息
 * 对应 DouyinLiveRecorder 中的流分析功能
 */
class StreamAnalyzer
{
    private FFProbe $ffprobe;

    public function __construct(
        private LoggerInterface $logger,
        ?array $ffprobeConfig = null
    ) {
        $this->initializeFFProbe($ffprobeConfig);
    }

    private function initializeFFProbe(?array $config = null): void
    {
        $defaultConfig = [
            'ffprobe.binaries' => $this->findFFprobeBinary(),
            'timeout' => 60,
        ];

        $config = array_merge($defaultConfig, $config ?? []);
        $this->ffprobe = FFProbe::create($config, $this->logger);
    }

    /**
     * 分析流信息
     */
    public function analyzeStream(string $streamUrl): array
    {
        try {
            $this->logger->debug("Analyzing stream", ['url' => $streamUrl]);

            // 获取格式信息
            $format = $this->ffprobe->format($streamUrl);

            // 获取流信息
            $streams = $this->ffprobe->streams($streamUrl);

            $analysis = [
                'url' => $streamUrl,
                'format' => $this->extractFormatInfo($format),
                'video_streams' => [],
                'audio_streams' => [],
                'total_streams' => count($streams),
                'duration' => $format->get('duration'),
                'bitrate' => $format->get('bit_rate'),
                'size' => $format->get('size'),
                'analyzed_at' => time(),
            ];

            // 分析各个流
            foreach ($streams as $stream) {
                if ($stream->isVideo()) {
                    $analysis['video_streams'][] = $this->extractVideoStreamInfo($stream);
                } elseif ($stream->isAudio()) {
                    $analysis['audio_streams'][] = $this->extractAudioStreamInfo($stream);
                }
            }

            $this->logger->info("Stream analysis completed", [
                'url' => $streamUrl,
                'video_streams' => count($analysis['video_streams']),
                'audio_streams' => count($analysis['audio_streams']),
                'format' => $analysis['format']['format_name'] ?? 'unknown',
            ]);

            return $analysis;
        } catch (\Exception $e) {
            $this->logger->error("Stream analysis failed", [
                'url' => $streamUrl,
                'error' => $e->getMessage(),
            ]);

            return [
                'url' => $streamUrl,
                'error' => $e->getMessage(),
                'analyzed_at' => time(),
            ];
        }
    }

    /**
     * 检查流是否可用
     */
    public function isStreamAvailable(string $streamUrl, int $timeout = 30): bool
    {
        try {
            $this->logger->debug("Checking stream availability", ['url' => $streamUrl]);

            // 设置临时超时
            $originalTimeout = $this->ffprobe->getFFProbeDriver()->getConfiguration()['timeout'] ?? 60;
            $this->ffprobe->getFFProbeDriver()->getConfiguration()['timeout'] = $timeout;

            // 尝试获取基本信息
            $format = $this->ffprobe->format($streamUrl);

            // 恢复原超时设置
            $this->ffprobe->getFFProbeDriver()->getConfiguration()['timeout'] = $originalTimeout;

            $isAvailable = $format && $format->get('format_name');

            $this->logger->debug("Stream availability check result", [
                'url' => $streamUrl,
                'available' => $isAvailable,
            ]);

            return $isAvailable;
        } catch (\Exception $e) {
            $this->logger->warning("Stream availability check failed", [
                'url' => $streamUrl,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 获取流的最佳质量
     */
    public function getBestQuality(string $streamUrl): ?array
    {
        try {
            $analysis = $this->analyzeStream($streamUrl);

            if (empty($analysis['video_streams'])) {
                return null;
            }

            // 找到最高分辨率的视频流
            $bestStream = null;
            $maxPixels = 0;

            foreach ($analysis['video_streams'] as $stream) {
                $pixels = ($stream['width'] ?? 0) * ($stream['height'] ?? 0);
                if ($pixels > $maxPixels) {
                    $maxPixels = $pixels;
                    $bestStream = $stream;
                }
            }

            return $bestStream;
        } catch (\Exception $e) {
            $this->logger->error("Failed to get best quality", [
                'url' => $streamUrl,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * 获取推荐的录制参数
     */
    public function getRecommendedRecordingParams(string $streamUrl): array
    {
        $analysis = $this->analyzeStream($streamUrl);
        $defaults = [
            'video_codec' => 'copy',
            'audio_codec' => 'copy',
            'format' => 'mp4',
            'resolution' => null,
            'bitrate' => null,
        ];

        if (isset($analysis['error'])) {
            return $defaults;
        }

        $params = $defaults;

        // 根据视频流推荐参数
        if (!empty($analysis['video_streams'])) {
            $videoStream = $analysis['video_streams'][0];

            // 如果分辨率过高，建议降低
            if (($videoStream['width'] ?? 0) > 1920) {
                $params['resolution'] = '1920x1080';
                $params['video_codec'] = 'libx264';
            }

            // 根据码率推荐格式
            $bitrate = $videoStream['bit_rate'] ?? 0;
            if ($bitrate > 8000000) { // 8Mbps 以上
                $params['format'] = 'mp4';
            } elseif ($bitrate > 2000000) { // 2-8Mbps
                $params['format'] = 'mp4';
            } else { // 2Mbps 以下
                $params['format'] = 'mp4';
            }
        }

        // 根据音频流推荐参数
        if (!empty($analysis['audio_streams'])) {
            $audioStream = $analysis['audio_streams'][0];

            // 如果音频码率过高，建议重新编码
            $audioBitrate = $audioStream['bit_rate'] ?? 0;
            if ($audioBitrate > 320000) { // 320kbps 以上
                $params['audio_codec'] = 'aac';
            }
        }

        $this->logger->info("Generated recommended recording params", [
            'url' => $streamUrl,
            'params' => $params,
        ]);

        return $params;
    }

    /**
     * 提取格式信息
     */
    private function extractFormatInfo(Format $format): array
    {
        return [
            'format_name' => $format->get('format_name'),
            'format_long_name' => $format->get('format_long_name'),
            'start_time' => $format->get('start_time'),
            'duration' => $format->get('duration'),
            'size' => $format->get('size'),
            'bit_rate' => $format->get('bit_rate'),
            'probe_score' => $format->get('probe_score'),
            'tags' => $format->getTags(),
        ];
    }

    /**
     * 提取视频流信息
     */
    private function extractVideoStreamInfo(Stream $stream): array
    {
        return [
            'index' => $stream->get('index'),
            'codec_name' => $stream->get('codec_name'),
            'codec_long_name' => $stream->get('codec_long_name'),
            'profile' => $stream->get('profile'),
            'codec_tag_string' => $stream->get('codec_tag_string'),
            'width' => $stream->get('width'),
            'height' => $stream->get('height'),
            'coded_width' => $stream->get('coded_width'),
            'coded_height' => $stream->get('coded_height'),
            'display_aspect_ratio' => $stream->get('display_aspect_ratio'),
            'sample_aspect_ratio' => $stream->get('sample_aspect_ratio'),
            'pix_fmt' => $stream->get('pix_fmt'),
            'level' => $stream->get('level'),
            'color_range' => $stream->get('color_range'),
            'color_space' => $stream->get('color_space'),
            'r_frame_rate' => $stream->get('r_frame_rate'),
            'avg_frame_rate' => $stream->get('avg_frame_rate'),
            'time_base' => $stream->get('time_base'),
            'bit_rate' => $stream->get('bit_rate'),
            'duration' => $stream->get('duration'),
            'tags' => $stream->getTags(),
        ];
    }

    /**
     * 提取音频流信息
     */
    private function extractAudioStreamInfo(Stream $stream): array
    {
        return [
            'index' => $stream->get('index'),
            'codec_name' => $stream->get('codec_name'),
            'codec_long_name' => $stream->get('codec_long_name'),
            'profile' => $stream->get('profile'),
            'codec_tag_string' => $stream->get('codec_tag_string'),
            'sample_fmt' => $stream->get('sample_fmt'),
            'sample_rate' => $stream->get('sample_rate'),
            'channels' => $stream->get('channels'),
            'channel_layout' => $stream->get('channel_layout'),
            'bits_per_sample' => $stream->get('bits_per_sample'),
            'r_frame_rate' => $stream->get('r_frame_rate'),
            'avg_frame_rate' => $stream->get('avg_frame_rate'),
            'time_base' => $stream->get('time_base'),
            'bit_rate' => $stream->get('bit_rate'),
            'duration' => $stream->get('duration'),
            'tags' => $stream->getTags(),
        ];
    }

    private function findFFprobeBinary(): string
    {
        $paths = ['/usr/bin/ffprobe', '/usr/local/bin/ffprobe', 'ffprobe'];

        foreach ($paths as $path) {
            if (is_executable($path)) {
                return $path;
            }
        }

        $result = shell_exec('which ffprobe 2>/dev/null');
        if ($result && is_executable(trim($result))) {
            return trim($result);
        }

        throw new RecordingException("FFprobe binary not found");
    }
}
