<?php

declare(strict_types=1);

namespace LiveStream\Recording;

use LiveStream\Contracts\RecorderInterface;
use LiveStream\Contracts\RoomInfoInterface;
use LiveStream\LiveStream;
use Psr\Log\LoggerInterface;
use Symfony\Component\Process\Process;

/**
 * 直播录制器
 * 
 * 基于 DouyinLiveRecorder main.py 的录制流程实现
 */
class LiveStreamRecorder implements RecorderInterface
{
    private array $activeRecords = [];
    private array $recordingProcesses = [];
    private string $defaultSavePath;
    private array $defaultOptions;

    public function __construct(
        private LiveStream $liveStream,
        private LoggerInterface $logger,
        string $defaultSavePath = './recordings'
    ) {
        $this->defaultSavePath = $defaultSavePath;
        $this->defaultOptions = [
            'quality' => '原画',
            'format' => 'mp4',
            'split_time' => null,
            'proxy' => null,
            'filename_template' => '{anchor_name}_{title}_{datetime}',
            'create_folder_by_platform' => true,
            'create_folder_by_author' => false,
            'create_folder_by_date' => false,
            'enable_https' => true,
            'timeout' => 300, // 5分钟超时
        ];
    }

    /**
     * 开始录制直播 (对应 Python start_record 函数)
     */
    public function startRecord(string $url, array $options = []): bool
    {
        try {
            $options = array_merge($this->defaultOptions, $options);
            $recordId = $this->generateRecordId($url);

            // 检查是否已在录制
            if (isset($this->activeRecords[$recordId])) {
                $this->logger->warning("Record already exists", ['url' => $url, 'record_id' => $recordId]);
                return false;
            }

            // 步骤1：获取直播间信息 (对应 Python 第517-528行)
            $this->logger->info("Getting live stream info", ['url' => $url]);
            $platform = $this->liveStream->driver($url);
            $roomInfo = $platform->getRoomInfo();

            // 步骤2：检查直播状态
            if (!$roomInfo->isLive()) {
                $this->logger->info("Stream is not live", [
                    'url' => $url,
                    'anchor_name' => $roomInfo->getAnchorName()
                ]);
                return false;
            }

            // 步骤3：获取流地址 (对应 Python 第527-528行)
            $streamUrls = $roomInfo->getStreamUrls($options['quality'], $options['proxy']);

            if (empty($streamUrls['record_url'])) {
                $this->logger->error("No record URL found", ['stream_urls' => $streamUrls]);
                return false;
            }

            // 步骤4：构建文件路径 (对应 Python 第1047-1077行)
            $filePath = $this->buildFilePath($roomInfo, $options);

            // 步骤5：构建 FFmpeg 命令 (对应 Python 第1104-1378行)
            $ffmpegCommand = $this->buildFFmpegCommand(
                $streamUrls['record_url'],
                $filePath,
                $options,
                $platform->getPlatformName(),
                $url
            );

            // 步骤6：启动录制进程 (对应 Python check_subprocess 函数)
            $process = $this->startRecordingProcess($ffmpegCommand, $recordId);

            // 步骤7：记录录制信息
            $this->activeRecords[$recordId] = [
                'url' => $url,
                'platform' => $platform->getPlatformName(),
                'anchor_name' => $roomInfo->getAnchorName(),
                'title' => $roomInfo->getTitle(),
                'quality' => $options['quality'],
                'file_path' => $filePath,
                'start_time' => time(),
                'options' => $options,
                'stream_info' => $streamUrls,
            ];

            $this->recordingProcesses[$recordId] = $process;

            $this->logger->info("Recording started successfully", [
                'record_id' => $recordId,
                'anchor_name' => $roomInfo->getAnchorName(),
                'file_path' => $filePath,
            ]);

            return true;
        } catch (\Throwable $e) {
            $this->logger->error("Failed to start recording", [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * 停止录制 (对应 Python 第402-413行的进程终止逻辑)
     */
    public function stopRecord(string $recordId): bool
    {
        if (!isset($this->activeRecords[$recordId])) {
            return false;
        }

        try {
            $process = $this->recordingProcesses[$recordId] ?? null;
            if ($process && $process->isRunning()) {
                // 优雅停止 FFmpeg
                $process->signal(SIGTERM);

                // 等待进程结束，最多等待10秒
                for ($i = 0; $i < 10; $i++) {
                    if (!$process->isRunning()) {
                        break;
                    }
                    sleep(1);
                }

                // 如果还在运行，强制终止
                if ($process->isRunning()) {
                    $process->stop();
                }
            }

            $recordInfo = $this->activeRecords[$recordId];
            $this->logger->info("Recording stopped", [
                'record_id' => $recordId,
                'anchor_name' => $recordInfo['anchor_name'],
                'duration' => time() - $recordInfo['start_time'],
            ]);

            unset($this->activeRecords[$recordId]);
            unset($this->recordingProcesses[$recordId]);

            return true;
        } catch (\Throwable $e) {
            $this->logger->error("Failed to stop recording", [
                'record_id' => $recordId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 获取录制状态
     */
    public function getRecordStatus(string $recordId): array
    {
        if (!isset($this->activeRecords[$recordId])) {
            return ['status' => 'not_found'];
        }

        $process = $this->recordingProcesses[$recordId] ?? null;
        $recordInfo = $this->activeRecords[$recordId];

        return [
            'status' => $process && $process->isRunning() ? 'recording' : 'stopped',
            'record_id' => $recordId,
            'platform' => $recordInfo['platform'],
            'anchor_name' => $recordInfo['anchor_name'],
            'title' => $recordInfo['title'],
            'file_path' => $recordInfo['file_path'],
            'start_time' => $recordInfo['start_time'],
            'duration' => time() - $recordInfo['start_time'],
            'quality' => $recordInfo['quality'],
        ];
    }

    /**
     * 获取所有正在录制的任务
     */
    public function getActiveRecords(): array
    {
        $activeRecords = [];

        foreach ($this->activeRecords as $recordId => $recordInfo) {
            $activeRecords[$recordId] = $this->getRecordStatus($recordId);
        }

        return $activeRecords;
    }

    /**
     * 生成录制ID
     */
    private function generateRecordId(string $url): string
    {
        return md5($url . time());
    }

    /**
     * 构建文件路径 (对应 Python 第1047-1077行)
     */
    private function buildFilePath(RoomInfoInterface $roomInfo, array $options): string
    {
        $basePath = $options['save_path'] ?? $this->defaultSavePath;
        $platform = $roomInfo instanceof \LiveStream\Platforms\Douyin\RoomInfo\DouyinRoomInfo ? '抖音直播' : '未知平台';

        // 清理文件名中的非法字符
        $anchorName = $this->cleanFileName($roomInfo->getAnchorName());
        $title = $this->cleanFileName($roomInfo->getTitle());

        // 构建目录路径
        $fullPath = $basePath;

        if ($options['create_folder_by_platform']) {
            $fullPath .= DIRECTORY_SEPARATOR . $platform;
        }

        if ($options['create_folder_by_author']) {
            $fullPath .= DIRECTORY_SEPARATOR . $anchorName;
        }

        if ($options['create_folder_by_date']) {
            $fullPath .= DIRECTORY_SEPARATOR . date('Y-m-d');
        }

        // 创建目录
        if (!is_dir($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // 构建文件名
        $datetime = date('Y-m-d_H-i-s');
        $extension = $this->getFileExtension($options['format']);

        $filename = str_replace([
            '{anchor_name}',
            '{title}',
            '{datetime}',
            '{quality}',
        ], [
            $anchorName,
            $title,
            $datetime,
            $options['quality'],
        ], $options['filename_template']);

        $filename = $this->cleanFileName($filename);

        if ($options['split_time']) {
            $filename .= '_%03d';
        }

        $filename .= '.' . $extension;

        return $fullPath . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * 构建 FFmpeg 命令
     */
    private function buildFFmpegCommand(string $streamUrl, string $filePath, array $options, string $platform, string $originalUrl): array
    {
        $builder = new FFmpegCommandBuilder();

        // 设置基本参数
        $builder->setInputUrl($streamUrl)
            ->setOutputPath($filePath);

        // 设置代理
        if ($options['proxy']) {
            $builder->setProxy($options['proxy']);
        }

        // 设置平台特定的请求头
        $liveDomain = parse_url($originalUrl, PHP_URL_SCHEME) . '://' . parse_url($originalUrl, PHP_URL_HOST);
        $headers = FFmpegCommandBuilder::getPlatformHeaders($platform, $liveDomain);
        if (!empty($headers)) {
            $builder->setHeaders($headers);
        }

        // 海外平台优化
        if (FFmpegCommandBuilder::isOverseasPlatform($originalUrl)) {
            $builder->setOverseasOptimization();
        }

        // HTTPS 处理 (对应 Python 第1079-1080行)
        if ($options['enable_https'] && str_starts_with($streamUrl, 'http://')) {
            $streamUrl = str_replace('http://', 'https://', $streamUrl);
            $builder->setInputUrl($streamUrl);
        }

        // 根据格式构建命令
        return match ($options['format']) {
            'mp4' => $options['split_time']
                ? $builder->buildForMp4Segments($options['split_time'])
                : $builder->buildForMp4(),
            'mkv' => $builder->buildForMkv(),
            'flv' => $builder->buildForTs(), // FLV 使用 TS 格式
            'ts' => $options['split_time']
                ? $builder->buildForTsSegments($options['split_time'])
                : $builder->buildForTs(),
            'mp3', 'aac' => $builder->buildForAudio($options['format']),
            default => $builder->buildForMp4(),
        };
    }

    /**
     * 启动录制进程 (对应 Python check_subprocess 函数)
     */
    private function startRecordingProcess(array $command, string $recordId): Process
    {
        $this->logger->debug("Starting FFmpeg process", ['command' => implode(' ', $command)]);

        $process = new Process($command);
        $process->setTimeout($this->defaultOptions['timeout']);
        $process->start();

        // 监控进程状态
        $this->monitorProcess($process, $recordId);

        return $process;
    }

    /**
     * 监控录制进程
     */
    private function monitorProcess(Process $process, string $recordId): void
    {
        // 在实际应用中，这里可以用异步方式监控进程状态
        // 比如使用 ReactPHP 或者后台任务队列

        // 简单的同步监控示例
        $process->wait(function ($type, $buffer) use ($recordId) {
            if (Process::ERR === $type) {
                $this->logger->error("FFmpeg error output", [
                    'record_id' => $recordId,
                    'output' => $buffer,
                ]);
            } else {
                $this->logger->debug("FFmpeg output", [
                    'record_id' => $recordId,
                    'output' => $buffer,
                ]);
            }
        });
    }

    /**
     * 清理文件名中的非法字符
     */
    private function cleanFileName(string $filename): string
    {
        if (empty($filename)) {
            return 'unnamed';
        }

        // 移除或替换非法字符
        $cleaned = preg_replace('/[<>:"\\/\\\\|?*]/', '_', $filename);
        $cleaned = preg_replace('/[\x00-\x1f\x7f]/', '', $cleaned);
        $cleaned = trim($cleaned, '. ');

        return $cleaned ?: 'unnamed';
    }

    /**
     * 获取文件扩展名
     */
    private function getFileExtension(string $format): string
    {
        return match ($format) {
            'mp4' => 'mp4',
            'mkv' => 'mkv',
            'flv' => 'flv',
            'ts' => 'ts',
            'mp3' => 'mp3',
            'aac' => 'aac',
            default => 'mp4',
        };
    }
}
