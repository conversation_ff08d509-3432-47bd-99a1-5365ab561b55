<?php

declare(strict_types=1);

namespace LiveStream;

use LiveStream\Contracts\PlatformInterface;
use LiveStream\Contracts\RecorderInterface;
use LiveStream\Contracts\RecordingResultInterface;
use LiveStream\Config\RecordingOptions;
use LiveStream\Factory\RecorderFactory;

/**
 * 直播流管理器
 * 
 * 提供高级 API，整合平台和录制功能
 */
final class LiveStreamManager
{
    public function __construct(
        private readonly LiveStream $liveStream,
        private readonly ?RecorderInterface $recorder = null
    ) {}

    /**
     * 快速录制直播流
     * 
     * @param string $url 直播间 URL
     * @param array|RecordingOptions $options 录制选项
     * @return RecordingResultInterface 录制结果
     */
    public function record(string $url, array|RecordingOptions $options = []): RecordingResultInterface
    {
        $platform = $this->liveStream->driver($url);
        $roomInfo = $platform->getRoomInfo();

        $recordingOptions = $options instanceof RecordingOptions
            ? $options
            : RecordingOptions::fromArray($options);

        $recorder = $this->getRecorder($url);

        return $recorder->record($roomInfo, $recordingOptions);
    }

    /**
     * 获取平台信息
     */
    public function getPlatform(string $url): PlatformInterface
    {
        return $this->liveStream->driver($url);
    }

    /**
     * 预览录制信息（不实际录制）
     * 
     * @param string $url 直播间 URL
     * @param array|RecordingOptions $options 录制选项
     * @return array 预览信息
     */
    public function preview(string $url, array|RecordingOptions $options = []): array
    {
        $platform = $this->liveStream->driver($url);
        $roomInfo = $platform->getRoomInfo();

        $recordingOptions = $options instanceof RecordingOptions
            ? $options
            : RecordingOptions::fromArray($options);

        $recorder = $this->getRecorder($url);

        // 尝试构建命令但不执行
        if (!$roomInfo->isLive()) {
            return [
                'status' => 'offline',
                'message' => 'Stream is not live',
                'room_info' => [
                    'anchor_name' => $roomInfo->getAnchorName(),
                    'title' => $roomInfo->getTitle(),
                    'room_id' => $roomInfo->getRoomId(),
                ],
            ];
        }

        $streamUrls = $roomInfo->getStreamUrls($recordingOptions->quality->getDisplayName(), $recordingOptions->proxy);

        return [
            'status' => 'ready',
            'message' => 'Ready to record',
            'room_info' => [
                'anchor_name' => $roomInfo->getAnchorName(),
                'title' => $roomInfo->getTitle(),
                'room_id' => $roomInfo->getRoomId(),
                'is_live' => $roomInfo->isLive(),
            ],
            'stream_info' => $streamUrls,
            'recording_options' => $recordingOptions->toArray(),
            'supported_formats' => $recorder->getSupportedFormats(),
        ];
    }

    /**
     * 验证录制环境
     */
    public function validateEnvironment(): array
    {
        $errors = [];

        // 验证录制器环境
        $recorder = $this->getRecorder();
        $recorderErrors = $recorder->validateEnvironment();
        $errors = array_merge($errors, $recorderErrors);

        return $errors;
    }

    /**
     * 获取录制器实例
     */
    private function getRecorder(?string $url = null): RecorderInterface
    {
        if ($this->recorder !== null) {
            return $this->recorder;
        }

        if ($url !== null) {
            return RecorderFactory::createForUrl($url);
        }

        return RecorderFactory::create();
    }

    /**
     * 创建管理器实例
     */
    public static function create(?RecorderInterface $recorder = null): self
    {
        $platformFactory = new PlatformFactory();
        $liveStream = new LiveStream($platformFactory);

        return new self($liveStream, $recorder);
    }

    /**
     * 获取支持的平台列表
     */
    public function getSupportedPlatforms(): array
    {
        return [
            'douyin' => [
                'name' => '抖音直播',
                'domains' => ['live.douyin.com', 'v.douyin.com', 'www.douyin.com'],
                'description' => '支持抖音直播录制',
            ],
            // 可以添加更多平台
        ];
    }
}
