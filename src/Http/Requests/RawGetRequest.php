<?php

declare(strict_types=1);

namespace LiveStream\Http\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

/**
 * 通用 GET 请求（支持绝对 URL、可传入自定义请求头与代理）
 */
class RawGetRequest extends Request
{
    protected Method $method = Method::GET;

    private string $url;
    private array $customHeaders;
    private ?string $proxy;

    public function __construct(string $url, array $headers = [], ?string $proxy = null)
    {
        $this->url = $url;
        $this->customHeaders = $headers;
        $this->proxy = $proxy;
    }

    public function resolveEndpoint(): string
    {
        // 直接返回绝对 URL
        return $this->url;
    }

    protected function defaultHeaders(): array
    {
        $default = [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
            'Accept-Language' => 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        ];

        return array_merge($default, $this->customHeaders);
    }

    protected function defaultConfig(): array
    {
        $config = [
            'timeout' => 15,
            'verify' => false,
            'allow_redirects' => true,
            'http_errors' => false,
        ];

        if (!empty($this->proxy)) {
            $config['proxy'] = $this->proxy;
        }

        return $config;
    }
}
