<?php

declare(strict_types=1);

namespace LiveStream\Http\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

/**
 * 通用 HEAD 请求（用于探测流地址可用性）
 */
class RawHeadRequest extends Request
{
    protected Method $method = Method::HEAD;

    private string $url;
    private array $customHeaders;
    private ?string $proxy;

    public function __construct(string $url, array $headers = [], ?string $proxy = null)
    {
        $this->url = $url;
        $this->customHeaders = $headers;
        $this->proxy = $proxy;
    }

    public function resolveEndpoint(): string
    {
        return $this->url;
    }

    protected function defaultHeaders(): array
    {
        $default = [
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0',
            'Accept-Language' => 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
        ];

        return array_merge($default, $this->customHeaders);
    }

    protected function defaultConfig(): array
    {
        $config = [
            'timeout' => 10,
            'verify' => false,
            'allow_redirects' => true,
            'http_errors' => false,
        ];

        if (!empty($this->proxy)) {
            $config['proxy'] = $this->proxy;
        }

        return $config;
    }
}
