<?php

declare(strict_types=1);

namespace LiveStream\Http\Connector;

use Saloon\Http\Connector;

/**
 * 直连 Connector：允许请求自身提供完整绝对 URL
 */
class DirectConnector extends Connector
{
    public function resolveBaseUrl(): string
    {
        // 返回空字符串，让 Request 的 resolveEndpoint 返回完整绝对 URL
        return '';
    }

    protected function defaultHeaders(): array
    {
        return [];
    }

    protected function defaultConfig(): array
    {
        return [
            'timeout' => 15,
            'verify' => false,
            'allow_redirects' => true,
            'http_errors' => false,
        ];
    }
}
