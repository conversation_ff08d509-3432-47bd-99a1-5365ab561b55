<?php

declare(strict_types=1);

namespace LiveStream\Validation;

use LiveStream\Config\RecordingOptions;
use LiveStream\Contracts\RoomInfoInterface;

/**
 * 配置验证器
 * 
 * 提供统一的配置验证逻辑
 */
final class ConfigValidator
{
    /**
     * 验证录制选项
     * 
     * @return array<string> 验证错误列表
     */
    public static function validateRecordingOptions(RecordingOptions $options): array
    {
        return $options->validate();
    }

    /**
     * 验证房间信息
     * 
     * @return array<string> 验证错误列表
     */
    public static function validateRoomInfo(RoomInfoInterface $roomInfo): array
    {
        $errors = [];

        if (empty($roomInfo->getAnchorName())) {
            $errors[] = 'Anchor name is required';
        }

        if (empty($roomInfo->getRoomId())) {
            $errors[] = 'Room ID is required';
        }

        return $errors;
    }

    /**
     * 验证 URL 格式
     * 
     * @return array<string> 验证错误列表
     */
    public static function validateUrl(string $url): array
    {
        $errors = [];

        if (empty($url)) {
            $errors[] = 'URL is required';
            return $errors;
        }

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid URL format';
        }

        // 检查协议
        $scheme = parse_url($url, PHP_URL_SCHEME);
        if (!in_array($scheme, ['http', 'https'], true)) {
            $errors[] = 'URL must use HTTP or HTTPS protocol';
        }

        return $errors;
    }

    /**
     * 验证文件路径
     * 
     * @return array<string> 验证错误列表
     */
    public static function validatePath(string $path): array
    {
        $errors = [];

        if (empty($path)) {
            $errors[] = 'Path is required';
            return $errors;
        }

        $directory = dirname($path);
        if (!is_dir($directory) && !mkdir($directory, 0755, true)) {
            $errors[] = "Cannot create directory: {$directory}";
        }

        if (is_dir($directory) && !is_writable($directory)) {
            $errors[] = "Directory is not writable: {$directory}";
        }

        return $errors;
    }

    /**
     * 验证代理设置
     * 
     * @return array<string> 验证错误列表
     */
    public static function validateProxy(?string $proxy): array
    {
        $errors = [];

        if ($proxy === null) {
            return $errors;
        }

        if (!filter_var($proxy, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid proxy URL format';
        }

        $scheme = parse_url($proxy, PHP_URL_SCHEME);
        if (!in_array($scheme, ['http', 'https', 'socks5'], true)) {
            $errors[] = 'Proxy must use HTTP, HTTPS, or SOCKS5 protocol';
        }

        return $errors;
    }

    /**
     * 综合验证
     * 
     * @return array<string> 验证错误列表
     */
    public static function validateAll(
        string $url,
        RoomInfoInterface $roomInfo,
        RecordingOptions $options
    ): array {
        $errors = [];

        $errors = array_merge($errors, self::validateUrl($url));
        $errors = array_merge($errors, self::validateRoomInfo($roomInfo));
        $errors = array_merge($errors, self::validateRecordingOptions($options));

        return $errors;
    }
}
