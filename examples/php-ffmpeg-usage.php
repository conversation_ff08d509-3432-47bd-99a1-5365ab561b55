<?php

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\AdvancedFFmpegRecorder;
use LiveStream\Recording\StreamAnalyzer;
use LiveStream\Recording\FFmpegCommandBuilder;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

echo "=== PHP FFmpeg 高级录制功能示例 ===\n\n";

// 创建日志器
$logger = new Logger('advanced_recorder');
$logger->pushHandler(new StreamHandler('php://stdout', Logger::INFO));
$logger->pushHandler(new StreamHandler('./advanced_recordings.log', Logger::DEBUG));

// 创建 LiveStream 实例
$platformFactory = new PlatformFactory();
$liveStream = new LiveStream($platformFactory);

echo "1. 流分析功能示例\n";
echo str_repeat('-', 40) . "\n";

// 创建流分析器
$analyzer = new StreamAnalyzer($logger);

// 示例流URL（实际使用时替换为真实的直播流地址）
$testStreamUrl = 'https://example.com/test.m3u8';

echo "分析流信息: $testStreamUrl\n";

// 检查流可用性
$isAvailable = $analyzer->isStreamAvailable($testStreamUrl, 10);
echo "流可用性: " . ($isAvailable ? '✅ 可用' : '❌ 不可用') . "\n";

if ($isAvailable) {
    // 分析流详细信息
    $analysis = $analyzer->analyzeStream($testStreamUrl);

    echo "流分析结果:\n";
    echo "- 格式: " . ($analysis['format']['format_name'] ?? '未知') . "\n";
    echo "- 视频流数量: " . count($analysis['video_streams'] ?? []) . "\n";
    echo "- 音频流数量: " . count($analysis['audio_streams'] ?? []) . "\n";

    if (!empty($analysis['video_streams'])) {
        $video = $analysis['video_streams'][0];
        echo "- 视频分辨率: {$video['width']}x{$video['height']}\n";
        echo "- 视频编码: " . ($video['codec_name'] ?? '未知') . "\n";
        echo "- 视频码率: " . ($video['bit_rate'] ?? '未知') . " bps\n";
    }

    if (!empty($analysis['audio_streams'])) {
        $audio = $analysis['audio_streams'][0];
        echo "- 音频编码: " . ($audio['codec_name'] ?? '未知') . "\n";
        echo "- 音频采样率: " . ($audio['sample_rate'] ?? '未知') . " Hz\n";
        echo "- 音频声道: " . ($audio['channels'] ?? '未知') . "\n";
    }

    // 获取推荐录制参数
    $recommendedParams = $analyzer->getRecommendedRecordingParams($testStreamUrl);
    echo "推荐录制参数:\n";
    echo json_encode($recommendedParams, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
}

echo "\n" . str_repeat('=', 50) . "\n\n";

echo "2. 高级 FFmpeg 命令构建示例\n";
echo str_repeat('-', 40) . "\n";

// 创建支持 php-ffmpeg 的命令构建器
try {
    $builder = new FFmpegCommandBuilder(true, $logger); // 启用 php-ffmpeg
    echo "✅ php-ffmpeg 初始化成功\n";

    // 创建不同格式的对象
    $formats = [
        'mp4' => ['video_codec' => 'libx264', 'audio_codec' => 'aac', 'video_bitrate' => 2000, 'audio_bitrate' => '128k'],
        'webm' => ['video_codec' => 'libvpx', 'audio_codec' => 'libvorbis'],
        'mp3' => ['audio_bitrate' => '320k'],
        'aac' => ['audio_bitrate' => '256k'],
    ];

    foreach ($formats as $formatName => $options) {
        echo "\n创建 $formatName 格式对象:\n";
        try {
            $format = $builder->createPhpFFmpegFormat($formatName, $options);
            echo "✅ $formatName 格式创建成功\n";

            // 显示格式信息
            if (method_exists($format, 'getVideoCodec')) {
                echo "- 视频编码: " . ($format->getVideoCodec() ?? 'default') . "\n";
            }
            if (method_exists($format, 'getAudioCodec')) {
                echo "- 音频编码: " . ($format->getAudioCodec() ?? 'default') . "\n";
            }
            if (method_exists($format, 'getKiloBitrate')) {
                echo "- 视频码率: " . ($format->getKiloBitrate() ?? 'default') . " kbps\n";
            }
            if (method_exists($format, 'getAudioKiloBitrate')) {
                echo "- 音频码率: " . ($format->getAudioKiloBitrate() ?? 'default') . " kbps\n";
            }
        } catch (Exception $e) {
            echo "❌ $formatName 格式创建失败: " . $e->getMessage() . "\n";
        }
    }
} catch (Exception $e) {
    echo "❌ php-ffmpeg 初始化失败: " . $e->getMessage() . "\n";
    echo "请确保已安装 FFmpeg 并且 php-ffmpeg/php-ffmpeg 包已正确安装\n";
}

echo "\n" . str_repeat('=', 50) . "\n\n";

echo "3. 高级录制器功能示例\n";
echo str_repeat('-', 40) . "\n";

try {
    // 创建高级录制器
    $recorder = new AdvancedFFmpegRecorder($liveStream, $logger, './advanced_recordings');
    echo "✅ 高级录制器初始化成功\n";

    // 高级录制选项
    $advancedOptions = [
        // 基础设置
        'quality' => '原画',
        'format' => 'mp4',

        // 编码设置
        'video_codec' => 'copy', // 直接复制，不重新编码
        'audio_codec' => 'copy',
        'video_bitrate' => null, // 保持原始码率
        'audio_bitrate' => '320k',

        // 分辨率和质量
        'resolution' => null, // 保持原始分辨率
        'max_width' => 1920,
        'max_height' => 1080,

        // 分段录制
        'split_time' => '1800', // 30分钟分段
        'segment_format' => 'mp4',
        'segment_pattern' => '%03d',

        // 网络优化
        'proxy' => null,
        'timeout' => 3600, // 1小时
        'reconnect_attempts' => 10,
        'reconnect_delay' => 5,

        // 缓冲优化
        'buffer_size' => '16000k', // 16MB 缓冲
        'analyze_duration' => '30000000', // 30秒分析
        'probe_size' => '15000000', // 15MB 探测

        // 文件组织
        'filename_template' => '{platform}_{anchor_name}_{quality}_{datetime}',
        'create_folder_by_platform' => true,
        'create_folder_by_author' => true,
        'create_folder_by_date' => true,
    ];

    echo "高级录制配置:\n";
    echo json_encode($advancedOptions, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

    // 演示不同场景的录制配置
    $scenarios = [
        '高质量录制' => [
            'video_codec' => 'libx264',
            'audio_codec' => 'aac',
            'video_bitrate' => 5000,
            'audio_bitrate' => '256k',
            'format' => 'mp4',
        ],
        '快速录制' => [
            'video_codec' => 'copy',
            'audio_codec' => 'copy',
            'format' => 'ts', // TS 格式更适合直播
        ],
        '音频录制' => [
            'format' => 'mp3',
            'audio_bitrate' => '320k',
        ],
        '移动端优化' => [
            'video_codec' => 'libx264',
            'resolution' => '720x1280', // 竖屏
            'video_bitrate' => 2000,
            'audio_bitrate' => '128k',
            'format' => 'mp4',
        ],
    ];

    echo "\n预设录制场景:\n";
    foreach ($scenarios as $name => $config) {
        echo "\n[$name]:\n";
        foreach ($config as $key => $value) {
            echo "  $key: $value\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 高级录制器初始化失败: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat('=', 50) . "\n\n";

echo "4. 实际录制示例（演示）\n";
echo str_repeat('-', 40) . "\n";

// 注意：以下代码仅为演示，实际使用时需要真实的直播URL
$demoUrl = 'https://live.douyin.com/123456789';
echo "演示URL: $demoUrl\n";
echo "注意：这只是演示代码，实际录制需要真实的直播URL\n\n";

// 录制代码示例
echo "录制代码示例:\n";
echo "```php\n";
echo "\$recorder = new AdvancedFFmpegRecorder(\$liveStream, \$logger);\n";
echo "\n";
echo "// 基础录制\n";
echo "\$success = \$recorder->startRecord(\$url);\n";
echo "\n";
echo "// 高级录制\n";
echo "\$options = [\n";
echo "    'quality' => '原画',\n";
echo "    'format' => 'mp4',\n";
echo "    'split_time' => '1800', // 30分钟分段\n";
echo "    'video_codec' => 'copy',\n";
echo "    'audio_codec' => 'aac',\n";
echo "    'proxy' => 'http://proxy.example.com:8080',\n";
echo "];\n";
echo "\$success = \$recorder->startRecord(\$url, \$options);\n";
echo "\n";
echo "// 监控录制状态\n";
echo "\$records = \$recorder->getActiveRecords();\n";
echo "foreach (\$records as \$recordId => \$status) {\n";
echo "    echo \"录制: {\$status['anchor_name']} - {\$status['status']}\\n\";\n";
echo "}\n";
echo "\n";
echo "// 停止录制\n";
echo "\$recorder->stopRecord(\$recordId);\n";
echo "```\n\n";

echo "5. 性能优化建议\n";
echo str_repeat('-', 40) . "\n";

$optimizations = [
    '硬件优化' => [
        '使用 SSD 存储录制文件',
        '确保足够的内存 (推荐 8GB+)',
        '多核 CPU 提升编码性能',
        '稳定的网络连接',
    ],
    '软件优化' => [
        '使用 copy 编码避免重新编码',
        '合理设置缓冲区大小',
        '启用硬件加速编码 (如 NVENC)',
        '定期清理临时文件',
    ],
    '录制策略' => [
        '根据网络状况选择质量',
        '使用分段录制避免大文件',
        '设置合理的重连参数',
        '监控磁盘空间使用',
    ],
    '并发管理' => [
        '限制同时录制的数量',
        '使用队列管理录制任务',
        '监控系统资源使用',
        '实现优雅的停机机制',
    ],
];

foreach ($optimizations as $category => $tips) {
    echo "\n[$category]:\n";
    foreach ($tips as $tip) {
        echo "  • $tip\n";
    }
}

echo "\n" . str_repeat('=', 50) . "\n\n";

echo "6. 故障排除\n";
echo str_repeat('-', 40) . "\n";

$troubleshooting = [
    'FFmpeg 未找到' => [
        '检查 FFmpeg 是否已安装',
        '确认 FFmpeg 在系统 PATH 中',
        '手动指定 FFmpeg 二进制路径',
    ],
    'php-ffmpeg 初始化失败' => [
        '确认 composer 已安装 php-ffmpeg/php-ffmpeg',
        '检查 PHP 扩展是否完整',
        '验证 FFmpeg 和 FFprobe 权限',
    ],
    '录制质量问题' => [
        '检查原始流的质量',
        '调整编码参数',
        '监控网络带宽',
        '优化缓冲设置',
    ],
    '录制中断' => [
        '增加重连次数和延时',
        '检查网络稳定性',
        '监控磁盘空间',
        '查看详细错误日志',
    ],
];

foreach ($troubleshooting as $problem => $solutions) {
    echo "\n问题: $problem\n";
    echo "解决方案:\n";
    foreach ($solutions as $solution) {
        echo "  • $solution\n";
    }
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "示例结束\n\n";

echo "🚀 快速开始:\n";
echo "1. 确保安装 FFmpeg: sudo apt install ffmpeg\n";
echo "2. 安装 php-ffmpeg: composer require php-ffmpeg/php-ffmpeg\n";
echo "3. 创建录制器: \$recorder = new AdvancedFFmpegRecorder(\$liveStream, \$logger)\n";
echo "4. 开始录制: \$recorder->startRecord(\$url, \$options)\n\n";

echo "📚 更多信息:\n";
echo "- php-ffmpeg 文档: https://github.com/PHP-FFMpeg/PHP-FFMpeg\n";
echo "- FFmpeg 文档: https://ffmpeg.org/documentation.html\n";
echo "- 项目录制指南: RECORDING_GUIDE.md\n";
