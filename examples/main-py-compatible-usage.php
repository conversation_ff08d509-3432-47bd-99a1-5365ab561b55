<?php

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\Platforms\DouyinPlatformNew;
use LiveStream\Exceptions\PlatformException;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

/**
 * 基于main.py录制流程的PHP实现示例
 * 
 * 这个示例展示了如何使用新设计的PlatformInterface接口
 * 来实现与DouyinLiveRecorder项目main.py相同的录制流程
 */

// 创建日志实例
$logger = new Logger('live-stream');
$logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));

// 创建平台实例
$platform = new DouyinPlatformNew($logger);

// 示例URL（请替换为真实的直播间URL）
$url = 'https://live.douyin.com/123456789';
$proxy = null; // 代理地址，如：'http://127.0.0.1:7890'
$cookies = null; // Cookie字符串
$quality = 'OD'; // 画质代码：OD(原画)|UHD(超清)|HD(高清)|SD(标清)|LD(流畅)

echo "=== 基于main.py流程的直播录制示例 ===\n\n";

try {
    // 步骤1：检查URL是否支持（对应main.py中的URL匹配逻辑）
    echo "1. 检查URL支持性:\n";
    if (!$platform->supportsUrl($url)) {
        throw new Exception("不支持的URL: {$url}");
    }
    echo "   ✓ URL支持: " . $platform->getPlatformName() . "\n\n";

    // 步骤2：获取房间信息（对应main.py中的spider模块调用）
    echo "2. 获取房间信息:\n";
    $roomInfo = $platform->getRoomInfo($url, $proxy, $cookies);
    
    $anchorName = $platform->getAnchorName($roomInfo);
    $title = $platform->getTitle($roomInfo);
    $roomId = $platform->getRoomId($roomInfo);
    
    echo "   主播名称: {$anchorName}\n";
    echo "   直播标题: {$title}\n";
    echo "   房间ID: {$roomId}\n\n";

    // 步骤3：检查直播状态（对应main.py中的is_live判断）
    echo "3. 检查直播状态:\n";
    $isLive = $platform->isLive($roomInfo);
    echo "   直播状态: " . ($isLive ? '正在直播' : '未在直播') . "\n\n";

    if (!$isLive) {
        echo "   主播未在直播，等待直播开始...\n";
        // 在实际应用中，这里会进入循环等待
        return;
    }

    // 步骤4：检查画质支持（对应main.py中的画质处理）
    echo "4. 检查画质支持:\n";
    if (!$platform->supportsQuality($quality)) {
        echo "   警告: 不支持画质 {$quality}，使用默认画质\n";
        $quality = 'OD';
    }
    echo "   使用画质: {$quality}\n";
    
    $qualityMapping = $platform->getQualityMapping();
    echo "   支持的画质: " . implode(', ', array_keys($qualityMapping)) . "\n\n";

    // 步骤5：获取流地址（对应main.py中的stream模块调用）
    echo "5. 获取流地址:\n";
    $streamUrls = $platform->getStreamUrls($roomInfo, $quality, $proxy);
    
    if (!$streamUrls['is_live']) {
        throw new Exception("无法获取流地址，直播可能已结束");
    }
    
    $recordUrl = $streamUrls['record_url'];
    $m3u8Url = $streamUrls['m3u8_url'];
    $flvUrl = $streamUrls['flv_url'];
    
    echo "   录制地址: {$recordUrl}\n";
    echo "   M3U8地址: {$m3u8Url}\n";
    echo "   FLV地址: {$flvUrl}\n\n";

    // 步骤6：验证流地址（对应main.py中的流地址检查）
    echo "6. 验证流地址:\n";
    $isValid = $platform->validateStreamUrl($recordUrl, $proxy);
    echo "   地址有效性: " . ($isValid ? '有效' : '无效') . "\n\n";

    if (!$isValid) {
        throw new Exception("流地址无效，无法开始录制");
    }

    // 步骤7：准备录制信息（对应main.py中的录制准备）
    echo "7. 准备录制信息:\n";
    $fileName = $platform->getRecordFileName($roomInfo, $quality, true);
    $recordHeaders = $platform->getRecordHeaders();
    $specialFormat = $platform->getSpecialRecordFormat();
    
    echo "   文件名: {$fileName}\n";
    echo "   特殊头信息: " . ($recordHeaders ? json_encode($recordHeaders) : '无') . "\n";
    echo "   特殊格式: " . ($specialFormat ?: '无') . "\n\n";

    // 步骤8：构建FFmpeg命令（对应main.py中的FFmpeg命令构建）
    echo "8. 构建录制命令:\n";
    $ffmpegCommand = buildFFmpegCommand($recordUrl, $fileName, $recordHeaders, $proxy);
    echo "   FFmpeg命令: " . implode(' ', $ffmpegCommand) . "\n\n";

    // 步骤9：开始录制（对应main.py中的录制执行）
    echo "9. 开始录制:\n";
    echo "   ✓ 录制准备完成\n";
    echo "   ✓ 主播: {$anchorName}\n";
    echo "   ✓ 画质: {$quality}\n";
    echo "   ✓ 文件: {$fileName}\n";
    echo "   ✓ 流地址: {$recordUrl}\n\n";
    
    echo "   注意: 这是示例代码，实际录制需要执行FFmpeg命令\n";
    echo "   在生产环境中，这里会调用: exec(implode(' ', \$ffmpegCommand))\n\n";

} catch (PlatformException $e) {
    echo "平台错误: " . $e->getMessage() . "\n";
    
    // 对应main.py中的重试逻辑
    $retryConfig = $platform->getRetryConfig();
    echo "重试配置: " . json_encode($retryConfig) . "\n";
    
} catch (Exception $e) {
    echo "其他错误: " . $e->getMessage() . "\n";
}

echo "=== 示例结束 ===\n";

/**
 * 构建FFmpeg命令（对应main.py中的FFmpeg命令构建逻辑）
 */
function buildFFmpegCommand(string $streamUrl, string $fileName, ?array $headers, ?string $proxy): array
{
    $command = [
        'ffmpeg', '-y',
        '-v', 'verbose',
        '-rw_timeout', '15000000',
        '-loglevel', 'error',
        '-hide_banner',
        '-user_agent', 'Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36',
        '-protocol_whitelist', 'rtmp,crypto,file,http,https,tcp,tls,udp,rtp,httpproxy',
        '-thread_queue_size', '1024',
        '-analyzeduration', '20000000',
        '-probesize', '10000000',
        '-fflags', '+discardcorrupt',
        '-re', '-i', $streamUrl,
        '-bufsize', '8000k',
        '-sn', '-dn',
        '-reconnect_delay_max', '60',
        '-reconnect_streamed', '-reconnect_at_eof',
        '-max_muxing_queue_size', '1024',
        '-correct_ts_overflow', '1',
        '-avoid_negative_ts', '1',
        '-c', 'copy',
        $fileName . '.mp4'
    ];

    // 添加代理支持
    if ($proxy) {
        array_splice($command, 1, 0, ['-http_proxy', $proxy]);
    }

    // 添加特殊头信息
    if ($headers) {
        foreach ($headers as $key => $value) {
            array_splice($command, 11, 0, ['-headers', "{$key}:{$value}"]);
        }
    }

    return $command;
}
