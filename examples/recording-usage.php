<?php

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\LiveStreamRecorder;
use LiveStream\Recording\RecordingManager;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// 创建日志器
$logger = new Logger('live_recorder');
$logger->pushHandler(new StreamHandler('php://stdout', Logger::INFO));
$logger->pushHandler(new StreamHandler('./recordings.log', Logger::DEBUG));

// 创建 LiveStream 实例
$platformFactory = new PlatformFactory();
$liveStream = new LiveStream($platformFactory);

// 创建录制器
$recorder = new LiveStreamRecorder($liveStream, $logger, './recordings');

echo "=== PHP 直播录制器示例 ===\n\n";

// 示例1：单个直播录制
echo "1. 单个直播录制示例\n";
$douyinUrl = 'https://live.douyin.com/123456789';

$options = [
    'quality' => '原画',
    'format' => 'mp4',
    'split_time' => null, // 不分段
    'filename_template' => '{anchor_name}_{title}_{datetime}',
    'create_folder_by_platform' => true,
    'create_folder_by_author' => true,
    'create_folder_by_date' => false,
];

echo "尝试录制: $douyinUrl\n";
$success = $recorder->startRecord($douyinUrl, $options);

if ($success) {
    echo "✅ 录制启动成功\n";

    // 检查录制状态
    $activeRecords = $recorder->getActiveRecords();
    foreach ($activeRecords as $recordId => $status) {
        echo "录制ID: $recordId\n";
        echo "主播: {$status['anchor_name']}\n";
        echo "标题: {$status['title']}\n";
        echo "文件路径: {$status['file_path']}\n";
        echo "状态: {$status['status']}\n\n";

        // 演示：录制5秒后停止
        echo "录制5秒后停止...\n";
        sleep(5);

        $stopped = $recorder->stopRecord($recordId);
        echo $stopped ? "✅ 录制已停止\n" : "❌ 停止录制失败\n";
    }
} else {
    echo "❌ 录制启动失败（可能是未在直播或网络问题）\n";
}

echo "\n" . str_repeat('-', 50) . "\n\n";

// 示例2：批量录制管理
echo "2. 批量录制管理示例\n";

$manager = new RecordingManager($recorder, $logger);

// 添加多个录制任务
$urlList = [
    [
        'url' => 'https://live.douyin.com/111111111',
        'options' => [
            'quality' => '超清',
            'format' => 'mp4',
            'filename_template' => '{anchor_name}_{datetime}',
        ]
    ],
    [
        'url' => 'https://v.douyin.com/shortlink1',
        'options' => [
            'quality' => '高清',
            'format' => 'ts',
            'split_time' => '300', // 5分钟分段
        ]
    ],
    'https://live.douyin.com/333333333', // 使用默认配置
];

$taskIds = $manager->addBatchRecordingTasks($urlList);
echo "添加了 " . count($taskIds) . " 个录制任务\n";

// 显示管理器状态
$status = $manager->getStatus();
echo "管理器状态:\n";
echo "- 总任务数: {$status['total_tasks']}\n";
echo "- 计划中: {$status['scheduled_count']}\n";
echo "- 录制中: {$status['recording_count']}\n";
echo "- 失败: {$status['failed_count']}\n";
echo "- 完成: {$status['completed_count']}\n\n";

// 模拟运行管理器一段时间
echo "模拟运行录制管理器...\n";
// 注意：在实际应用中，应该在后台运行 $manager->start()
// 这里只是演示如何使用

echo "\n" . str_repeat('-', 50) . "\n\n";

// 示例3：不同格式录制
echo "3. 不同格式录制示例\n";

$formats = [
    'mp4' => '最常用的视频格式',
    'ts' => '流媒体传输格式，支持分段',
    'mkv' => '开源容器格式',
    'mp3' => '仅录制音频',
];

foreach ($formats as $format => $description) {
    echo "格式: $format - $description\n";

    $formatOptions = [
        'format' => $format,
        'quality' => '原画',
        'filename_template' => "test_{$format}_{datetime}",
        'create_folder_by_platform' => true,
    ];

    // 这里只是演示配置，实际录制需要真实的直播URL
    echo "配置示例: " . json_encode($formatOptions, JSON_UNESCAPED_UNICODE) . "\n\n";
}

echo "\n" . str_repeat('-', 50) . "\n\n";

// 示例4：高级配置
echo "4. 高级配置示例\n";

$advancedOptions = [
    'quality' => '原画',
    'format' => 'mp4',
    'split_time' => '1800', // 30分钟分段
    'proxy' => null, // 'http://proxy.example.com:8080'
    'filename_template' => '{platform}_{anchor_name}_{title}_{datetime}',
    'create_folder_by_platform' => true,
    'create_folder_by_author' => true,
    'create_folder_by_date' => true,
    'enable_https' => true,
    'timeout' => 1800, // 30分钟超时
    'max_retries' => 5,
    'save_path' => './my_recordings',
];

echo "高级配置示例:\n";
echo json_encode($advancedOptions, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "使用说明:\n";
echo "1. quality: 画质选择（原画、超清、高清、标清等）\n";
echo "2. format: 输出格式（mp4、ts、mkv、mp3、aac）\n";
echo "3. split_time: 分段时间（秒），null表示不分段\n";
echo "4. proxy: 代理服务器地址（如需要）\n";
echo "5. filename_template: 文件名模板，支持变量替换\n";
echo "6. create_folder_by_*: 文件夹组织方式\n";
echo "7. enable_https: 是否强制使用HTTPS\n";
echo "8. timeout: FFmpeg录制超时时间\n";
echo "9. max_retries: 最大重试次数\n";
echo "10. save_path: 保存路径\n\n";

echo "=== 示例结束 ===\n\n";

echo "💡 提示:\n";
echo "1. 确保系统已安装 FFmpeg\n";
echo "2. 确保有足够的磁盘空间\n";
echo "3. 在生产环境中，建议使用队列系统管理录制任务\n";
echo "4. 可以配合 ReactPHP 实现异步录制管理\n";
echo "5. 建议添加录制完成后的后处理逻辑（如转码、上传等）\n";
