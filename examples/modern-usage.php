<?php

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\LiveStreamManager;
use LiveStream\Config\RecordingOptions;
use LiveStream\Enum\Quality;
use LiveStream\Enum\OutputFormat;
use LiveStream\Factory\RecorderFactory;
use Symfony\Component\Process\Process;

echo "=== 现代化 PHP 直播录制示例 ===\n\n";

// 示例1：使用高级管理器
echo "1. 使用 LiveStreamManager 进行简单录制\n";
echo str_repeat('-', 50) . "\n";

$manager = LiveStreamManager::create();

// 验证环境
$envErrors = $manager->validateEnvironment();
if (!empty($envErrors)) {
    echo "环境验证失败:\n";
    foreach ($envErrors as $error) {
        echo "  - {$error}\n";
    }
    echo "\n";
}

$url = 'https://live.douyin.com/679309292214';

// 预览录制信息
echo "预览录制信息...\n";
$preview = $manager->preview($url);
echo "状态: {$preview['status']}\n";
echo "消息: {$preview['message']}\n";

if ($preview['status'] === 'ready') {
    echo "主播: {$preview['room_info']['anchor_name']}\n";
    echo "标题: {$preview['room_info']['title']}\n";
    echo "支持格式: " . implode(', ', $preview['supported_formats']) . "\n";
}

echo "\n";

// 示例2：使用配置对象
echo "2. 使用强类型配置对象\n";
echo str_repeat('-', 50) . "\n";

$options = new RecordingOptions(
    quality: Quality::ORIGINAL,
    format: OutputFormat::MP4,
    savePath: './recordings',
    filenameTemplate: '{anchor_name}_{title}_{datetime}',
    splitTime: 3600, // 1小时分段
    createFolderByPlatform: true,
    createFolderByAuthor: true,
    timeoutSeconds: 300,
    maxRetries: 3,
);

// 验证配置
$configErrors = $options->validate();
if (!empty($configErrors)) {
    echo "配置验证失败:\n";
    foreach ($configErrors as $error) {
        echo "  - {$error}\n";
    }
} else {
    echo "配置验证通过\n";
}

echo "配置详情:\n";
echo "  画质: {$options->quality->getDisplayName()}\n";
echo "  格式: {$options->format->getDescription()}\n";
echo "  保存路径: {$options->getSavePath()}\n";
echo "  文件名模板: {$options->getFilenameTemplate()}\n";

echo "\n";

// 示例3：执行录制并处理结果
echo "3. 执行录制（演示）\n";
echo str_repeat('-', 50) . "\n";

$result = $manager->record($url, $options);

if ($result->isSuccessful()) {
    echo "✅ 录制命令构建成功!\n";
    echo "输出文件: {$result->getOutputPath()}\n";
    echo "FFmpeg 命令:\n";
    echo "  " . implode(' ', array_slice($result->getCommand(), 0, 10)) . " ...\n";

    // 客户端可以选择如何执行命令
    echo "\n客户端执行选项:\n";
    echo "1. 使用 Symfony Process:\n";
    echo "   \$process = new Process(\$result->getCommand());\n";
    echo "   \$process->start();\n";

    echo "\n2. 使用 Swoole:\n";
    echo "   Swoole\\Process::exec('ffmpeg', array_slice(\$command, 1));\n";

    echo "\n3. 直接执行:\n";
    echo "   exec(implode(' ', \$result->getCommand()));\n";
} else {
    echo "❌ 录制失败: {$result->getError()}\n";
}

echo "\n";

// 示例4：工厂模式创建录制器
echo "4. 使用工厂模式创建特定录制器\n";
echo str_repeat('-', 50) . "\n";

// 为海外平台创建优化的录制器
$overseasRecorder = RecorderFactory::createForOverseas();
echo "创建海外平台录制器: " . get_class($overseasRecorder) . "\n";

// 根据 URL 自动选择录制器
$autoRecorder = RecorderFactory::createForUrl('https://www.tiktok.com/@user/live');
echo "自动选择录制器: " . get_class($autoRecorder) . "\n";

echo "\n";

// 示例5：配置修改
echo "5. 配置对象的不可变更新\n";
echo str_repeat('-', 50) . "\n";

$newOptions = $options->with(
    quality: Quality::HIGH,
    format: OutputFormat::MKV,
    splitTime: null // 取消分段
);

echo "原始配置画质: {$options->quality->getDisplayName()}\n";
echo "新配置画质: {$newOptions->quality->getDisplayName()}\n";
echo "原始配置格式: {$options->format->value}\n";
echo "新配置格式: {$newOptions->format->value}\n";

echo "\n";

// 示例6：平台信息获取
echo "6. 获取平台信息\n";
echo str_repeat('-', 50) . "\n";

$supportedPlatforms = $manager->getSupportedPlatforms();
foreach ($supportedPlatforms as $key => $platform) {
    echo "平台: {$platform['name']} ({$key})\n";
    echo "  域名: " . implode(', ', $platform['domains']) . "\n";
    echo "  描述: {$platform['description']}\n";
}

echo "\n=== 示例结束 ===\n";

// 实际使用中的完整流程示例
function demonstrateCompleteWorkflow(): void
{
    echo "\n=== 完整工作流程演示 ===\n";

    // 1. 创建管理器
    $manager = LiveStreamManager::create();

    // 2. 验证环境
    $errors = $manager->validateEnvironment();
    if (!empty($errors)) {
        throw new RuntimeException('Environment validation failed: ' . implode(', ', $errors));
    }

    // 3. 创建配置
    $options = new RecordingOptions(
        quality: Quality::ORIGINAL,
        format: OutputFormat::MP4,
        savePath: './downloads',
        createFolderByPlatform: true,
    );

    // 4. 执行录制
    $url = 'https://live.douyin.com/example';
    $result = $manager->record($url, $options);

    if ($result->isSuccessful()) {
        // 5. 客户端选择执行方式
        $command = $result->getCommand();

        // 使用 Symfony Process 执行（推荐）
        $process = new Process($command);
        $process->setTimeout(null); // 无超时限制

        echo "开始录制: {$result->getOutputPath()}\n";

        // 在实际应用中，这里会启动进程
        // $process->start();

        // 可以监控进程状态
        // while ($process->isRunning()) {
        //     echo "录制中...\n";
        //     sleep(10);
        // }

        echo "录制命令已准备就绪\n";
    } else {
        echo "录制失败: {$result->getError()}\n";
    }
}

// 取消注释以运行完整演示
// demonstrateCompleteWorkflow();