<?php

require_once __DIR__ . '/../vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\Exceptions\PlatformException;

// 创建实例
$liveStream = new LiveStream();

// 示例URL（请替换为真实的直播间URL）
$url = 'https://live.douyin.com/123456789';

echo "=== Live Stream PHP Library 示例 ===\n\n";

// 1. 获取直播数据
echo "1. 获取直播数据:\n";
try {
    $liveData = $liveStream->getLiveData($url);
    echo "   主播: " . ($liveData['anchor_name'] ?? '未知') . "\n";
    echo "   标题: " . ($liveData['title'] ?? '未知') . "\n";
    echo "   状态: " . ($liveData['status'] ?? '未知') . "\n";
    echo "   是否直播: " . (($liveData['status'] ?? 4) == 2 ? '是' : '否') . "\n";
} catch (PlatformException $e) {
    echo "   平台错误: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "   其他错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. 检查直播状态
echo "2. 检查直播状态:\n";
try {
    $isLive = $liveStream->isLive($url);
    echo "   状态: " . ($isLive ? '正在直播' : '未在直播') . "\n";
} catch (Exception $e) {
    echo "   错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 3. 获取主播信息
echo "3. 获取主播信息:\n";
try {
    $anchorInfo = $liveStream->getAnchorInfo($url);
    echo "   平台: " . $anchorInfo['platform'] . "\n";
    echo "   主播: " . $anchorInfo['anchor_name'] . "\n";
    echo "   标题: " . $anchorInfo['title'] . "\n";
    echo "   状态: " . ($anchorInfo['is_live'] ? '直播中' : '未直播') . "\n";
} catch (Exception $e) {
    echo "   错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 4. 获取流地址（如果正在直播）
echo "4. 获取流地址:\n";
try {
    $streamData = $liveStream->getStreamUrl($url, '原画');

    if ($streamData['is_live']) {
        echo "   画质: " . $streamData['quality'] . "\n";
        echo "   M3U8地址: " . $streamData['m3u8_url'] . "\n";
        echo "   FLV地址: " . $streamData['flv_url'] . "\n";
        echo "   录制地址: " . $streamData['record_url'] . "\n";
    } else {
        echo "   未在直播，无法获取流地址\n";
    }
} catch (PlatformException $e) {
    echo "   平台错误: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "   其他错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 5. 获取支持的平台
echo "5. 支持的平台:\n";
$platforms = $liveStream->getSupportedPlatforms();
foreach ($platforms as $platform) {
    echo "   - " . $platform . "\n";
}

echo "\n=== 示例结束 ===\n";
