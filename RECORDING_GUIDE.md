# PHP 直播录制功能使用指南

## 概述

本项目基于 DouyinLiveRecorder Python 项目的录制流程，使用 PHP 实现了完整的直播录制功能，包括：

- 🎯 **多平台支持**: 抖音、TikTok、B站等主流平台
- 🔧 **FFmpeg 集成**: 基于 FFmpeg 的专业录制能力
- 📁 **智能文件管理**: 支持多种文件组织方式
- 🔄 **自动重试**: 内置错误处理和重试机制
- 🎛️ **灵活配置**: 支持画质、格式、分段等多种选项

## 系统要求

### 必需组件

1. **PHP 8.1+**
2. **FFmpeg**: 录制核心引擎
   ```bash
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg
   
   # CentOS/RHEL
   sudo yum install ffmpeg
   
   # macOS
   brew install ffmpeg
   
   # Windows
   # 从 https://ffmpeg.org/download.html 下载
   ```

3. **Composer 依赖**:
   ```bash
   composer require symfony/process
   ```

### 可选组件

- **磁盘空间**: 根据录制时长预留足够空间
- **代理服务**: 某些平台可能需要代理访问

## 快速开始

### 基础用法

```php
<?php
require_once 'vendor/autoload.php';

use LiveStream\LiveStream;
use LiveStream\PlatformFactory;
use LiveStream\Recording\LiveStreamRecorder;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// 1. 创建日志器
$logger = new Logger('recorder');
$logger->pushHandler(new StreamHandler('recordings.log'));

// 2. 创建 LiveStream 实例
$liveStream = new LiveStream(new PlatformFactory());

// 3. 创建录制器
$recorder = new LiveStreamRecorder($liveStream, $logger);

// 4. 开始录制
$success = $recorder->startRecord('https://live.douyin.com/实际房间号');

if ($success) {
    echo "录制已启动！\n";
    
    // 查看录制状态
    $records = $recorder->getActiveRecords();
    foreach ($records as $recordId => $status) {
        echo "录制ID: $recordId\n";
        echo "主播: {$status['anchor_name']}\n";
        echo "状态: {$status['status']}\n";
    }
} else {
    echo "录制启动失败\n";
}
```

### 高级配置

```php
$options = [
    // 录制质量
    'quality' => '原画',              // 原画、超清、高清、标清
    
    // 输出格式
    'format' => 'mp4',               // mp4、ts、mkv、mp3、aac
    
    // 分段录制（秒）
    'split_time' => '1800',          // 30分钟分段，null=不分段
    
    // 代理设置
    'proxy' => 'http://proxy:8080',  // HTTP代理地址
    
    // 文件命名
    'filename_template' => '{anchor_name}_{title}_{datetime}',
    
    // 文件夹组织
    'create_folder_by_platform' => true,  // 按平台分类
    'create_folder_by_author' => true,    // 按主播分类  
    'create_folder_by_date' => false,     // 按日期分类
    
    // 其他选项
    'enable_https' => true,          // 强制HTTPS
    'timeout' => 1800,               // 超时时间（秒）
    'save_path' => './recordings',   // 保存路径
];

$recorder->startRecord($url, $options);
```

## 批量录制管理

### 录制管理器

```php
use LiveStream\Recording\RecordingManager;

// 创建管理器
$manager = new RecordingManager($recorder, $logger);

// 添加批量任务
$urlList = [
    [
        'url' => 'https://live.douyin.com/111111',
        'options' => ['quality' => '超清', 'format' => 'mp4']
    ],
    [
        'url' => 'https://live.douyin.com/222222', 
        'options' => ['quality' => '高清', 'split_time' => '300']
    ],
    'https://live.douyin.com/333333', // 使用默认配置
];

$taskIds = $manager->addBatchRecordingTasks($urlList);

// 启动管理器（在后台运行）
// $manager->start(); // 注意：这会阻塞当前进程
```

### 任务监控

```php
// 获取管理器状态
$status = $manager->getStatus();
echo "总任务: {$status['total_tasks']}\n";
echo "录制中: {$status['recording_count']}\n";
echo "已完成: {$status['completed_count']}\n";

// 获取任务详情
foreach ($taskIds as $taskId) {
    $details = $manager->getTaskDetails($taskId);
    echo "任务 $taskId: {$details['status']}\n";
}

// 取消任务
$manager->cancelTask($taskId);

// 停止管理器
$manager->stop();
```

## 录制格式详解

### 视频格式

| 格式 | 描述 | 适用场景 | 文件大小 |
|------|------|----------|----------|
| **MP4** | 最通用的格式 | 通用播放、后期处理 | 中等 |
| **TS** | 流媒体格式 | 直播传输、分段录制 | 较大 |
| **MKV** | 开源容器 | 高质量存档 | 较大 |
| **FLV** | Flash 格式 | 特定平台兼容 | 较小 |

### 音频格式

| 格式 | 描述 | 比特率 |
|------|------|--------|
| **MP3** | 通用音频格式 | 320kbps |
| **AAC** | 高效音频编码 | 320kbps |

### 分段录制

```php
// 按时间分段（推荐用于长时间录制）
'split_time' => '1800', // 30分钟一个文件

// 文件命名格式：主播名_日期时间_001.mp4
//              主播名_日期时间_002.mp4
//              ...
```

## 文件组织策略

### 目录结构示例

```
recordings/
├── 抖音直播/                    # platform
│   ├── 2024-01-15/             # date
│   │   ├── 主播A/              # author
│   │   │   ├── 主播A_标题_2024-01-15_20-30-00.mp4
│   │   │   └── 主播A_标题_2024-01-15_21-00-00.mp4
│   │   └── 主播B/
│   │       └── 主播B_标题_2024-01-15_19-00-00.mp4
│   └── 2024-01-16/
└── TikTok直播/
    └── 2024-01-15/
```

### 文件命名变量

| 变量 | 说明 | 示例 |
|------|------|------|
| `{platform}` | 平台名称 | 抖音直播 |
| `{anchor_name}` | 主播名称 | 张三 |
| `{title}` | 直播标题 | 今晚唱歌 |
| `{datetime}` | 录制时间 | 2024-01-15_20-30-00 |
| `{quality}` | 录制画质 | 原画 |

## 错误处理

### 常见错误及解决方案

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| `FFmpeg not found` | FFmpeg 未安装 | 安装 FFmpeg |
| `Failed to start recording` | 直播未开始或网络问题 | 检查直播状态和网络 |
| `Insufficient disk space` | 磁盘空间不足 | 清理磁盘空间 |
| `Permission denied` | 文件权限问题 | 检查目录写权限 |

### 自动重试配置

```php
$options = [
    'max_retries' => 5,              // 最大重试次数
    'retry_delay' => 60,             // 重试间隔（秒）
    'timeout' => 1800,               // 单次录制超时
];
```

## 性能优化

### 系统资源

1. **CPU**: FFmpeg 转码消耗 CPU 资源
2. **内存**: 一般每个录制任务消耗 50-100MB
3. **磁盘**: 
   - 原画质：约 2-4GB/小时
   - 高清质：约 1-2GB/小时
   - 标清质：约 0.5-1GB/小时

### 并发录制

```php
// 建议同时录制数量
$maxConcurrent = min(8, $cpuCores * 2);

// 监控系统资源
$load = sys_getloadavg()[0];
if ($load > $cpuCores * 0.8) {
    // 暂停新任务启动
}
```

### 海外平台优化

```php
// 自动检测海外平台并优化参数
if (FFmpegCommandBuilder::isOverseasPlatform($url)) {
    $options['timeout'] = 3600;      // 增加超时时间
    $options['proxy'] = $globalProxy; // 使用代理
}
```

## 最佳实践

### 1. 生产环境部署

```php
// 使用队列系统管理录制任务
// 推荐使用 Laravel Horizon、Symfony Messenger 等

// 监控录制状态
$healthCheck = function() use ($manager) {
    $status = $manager->getStatus();
    if ($status['failed_count'] > 10) {
        // 发送告警
    }
};
```

### 2. 日志配置

```php
use Monolog\Logger;
use Monolog\Handler\RotatingFileHandler;

$logger = new Logger('recorder');
$logger->pushHandler(new RotatingFileHandler(
    'logs/recording.log',
    7,  // 保留7天
    Logger::INFO
));
```

### 3. 配置文件

```php
// config/recording.php
return [
    'default_quality' => '原画',
    'default_format' => 'mp4', 
    'max_concurrent' => 5,
    'storage_path' => env('RECORDING_PATH', './recordings'),
    'ffmpeg_timeout' => 3600,
    'retry_attempts' => 3,
];
```

### 4. 监控脚本

```bash
#!/bin/bash
# monitor.sh - 监控录制进程

ps aux | grep ffmpeg | grep -v grep
df -h | grep recordings
free -h
```

## 疑难解答

### Q: 录制的文件很大怎么办？
A: 
1. 降低录制画质
2. 使用更高效的编码格式（H.265）
3. 启用分段录制

### Q: 录制经常中断怎么办？
A:
1. 检查网络稳定性
2. 增加超时时间
3. 使用代理服务器

### Q: 如何实现自动录制？
A:
1. 使用 cron 定时任务
2. 监控直播状态API
3. 结合消息队列系统

### Q: 支持录制回放吗？
A: 当前版本专注于实时录制，回放录制需要额外开发。

## 开发扩展

### 自定义录制器

```php
class CustomRecorder extends LiveStreamRecorder 
{
    public function afterRecordComplete(string $filePath): void 
    {
        // 自定义后处理逻辑
        $this->uploadToCloud($filePath);
        $this->generateThumbnail($filePath);
    }
}
```

### 添加新平台支持

1. 实现 `PlatformInterface`
2. 创建对应的 `RoomInfo` 类
3. 在 `PlatformFactory` 中注册

## 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目基于 MIT 许可证开源。